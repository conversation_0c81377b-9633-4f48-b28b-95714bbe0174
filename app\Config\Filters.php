<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;
use CodeIgniter\Filters\CSRF;
use CodeIgniter\Filters\DebugToolbar;
use CodeIgniter\Filters\ForceHTTPS;
use CodeIgniter\Filters\Honeypot;
use CodeIgniter\Filters\InvalidChars;
use CodeIgniter\Filters\PageCache;
use CodeIgniter\Filters\PerformanceMetrics;
use CodeIgniter\Filters\SecureHeaders;

class Filters extends BaseConfig
{
    /**
     * Configures aliases for Filter classes to
     * make reading things nicer and simpler.
     */
    public array $aliases = [
        'csrf'          => CSRF::class,
        'toolbar'       => DebugToolbar::class,
        'honeypot'      => Honeypot::class,
        'invalidchars'  => InvalidChars::class,
        'secureheaders' => SecureHeaders::class,
        'forcehttps'    => ForceHTTPS::class,
        'pagecache'     => PageCache::class,
        'performance'   => PerformanceMetrics::class,

        'auth' => \App\Filters\Auth::class,
        'admin_auth' => \App\Filters\AdminAuth::class,
        'dakoii_auth' => \App\Filters\DakoiiAuth::class,
        //'expired' => \App\Filters\Expired::class,
        //'authemp' => \App\Filters\Authemp::class,
    ];

    /**
     * List of filter aliases that are always
     * applied before and after every request.
     */
    public array $globals = [
        'before' => [
            'auth' => [
                'except' => [
                    '/',
                    'login',
                    'about',
                    'staff_login',
                    'admin',
                    'admin/*',            // ✅ Exclude ALL Admin routes
                    'dakoii',
                    'dakoii/*',           // ✅ Exclude ALL Dakoii routes
                    'assets/*',
                    'public/*'
                ]
            ],
        ],
        'after' => [
        ],
    ];

    /**
     * List of filter aliases that should run on any
     * before or after URI patterns.
     */
    public array $required = [
        'before' => [
            // Disable filters that might cause memory issues
            // 'forcehttps', // Force Global Secure Requests
            // 'pagecache',  // Web Page Caching
        ],
        'after' => [
            // Disable filters that might cause memory issues
            // 'pagecache',   // Web Page Caching
            // 'performance', // Performance Metrics
            // 'toolbar',     // Debug Toolbar
        ],
    ];

    /**
     * List of filter aliases that works on a
     * particular HTTP method (GET, POST, etc.).
     *
     * Example:
     * 'post' => ['foo', 'bar']
     *
     * If you use this, you should disable auto-routing because auto-routing
     * permits any HTTP method to access a controller. Accessing the controller
     * with a method you don't expect could bypass the filter.
     */
    public array $methods = [
        'POST' => ['invalidchars', 'csrf'],
        'GET'  => ['csrf'],
    ];

    /**
     * List of filter aliases that should run on any
     * before or after URI patterns.
     *
     * Example:
     * 'isLoggedIn' => ['before' => ['account/*', 'profiles/*']]
     */
    public array $filters = [
        'auth' => [
            'before' => [
                'employees/*',
                'reports/*',
                'settings/*',
                'staff/*',
                'users/*',
                'groups/*',
                'plans-management/*',
                'projects/*',
                'workplans/*',
                'dashboards/*',
                'farmers/*',
                'location/*',
                'crops-*',
                'exercises/*'
                // Add other main app protected routes/paths
            ]
        ],
        'admin_auth' => [
            'before' => [
                'dashboard',
                'admin/dashboard',
                'admin/users/*',
                'admin/groups/*',
                'admin/organization/*',
                'admin/data/*',
                'admin/reports/*',
                'admin/field-users/*',
                'admin/settings/*'
            ]
        ],
        'dakoii_auth' => [
            'before' => [
                'dakoii/dashboard',
                'dakoii/data/*',
                'dakoii/organizations/*',
                'dakoii/users/*',
                'dakoii/system-admins/*',
                'dakoii/locations/*'
            ]
        ]
    ];
}
