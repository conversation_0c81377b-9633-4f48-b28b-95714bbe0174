<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UsersModel;

class AdminAuth extends BaseController
{
    protected $usersModel;
    protected $session;

    public function __construct()
    {
        $this->usersModel = new UsersModel();
        $this->session = session();
        helper(['form', 'url']);
    }

    /**
     * Redirect to dashboard if already logged in, otherwise show login
     */
    public function index()
    {
        if ($this->session->has('logged_in') && $this->session->get('is_admin') == 1) {
            return redirect()->to('admin/dashboard');
        }
        
        return redirect()->to('admin/login');
    }

    /**
     * Display admin login form
     */
    public function login()
    {
        // Redirect if already logged in as admin
        if ($this->session->has('logged_in') && $this->session->get('is_admin') == 1) {
            return redirect()->to('admin/dashboard');
        }

        $data = [
            'title' => 'Admin Login',
            'page_header' => 'Admin Portal Login',
            'menu' => 'admin-login'
        ];

        return view('admin/auth/admin_login', $data);
    }

    /**
     * Process admin login
     */
    public function processLogin()
    {
        $rules = [
            'username' => 'required|min_length[3]',
            'password' => 'required|min_length[1]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('error', 'Please enter valid username and password');
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');

        // Find user by sys_no or email
        $user = $this->usersModel->where('sys_no', $username)
                                 ->orWhere('email', $username)
                                 ->first();

        if (!$user) {
            return redirect()->back()->withInput()->with('error', 'Invalid username or password');
        }

        // Verify password
        if (!password_verify($password, $user['password'])) {
            return redirect()->back()->withInput()->with('error', 'Invalid username or password');
        }

        // Check if user is admin
        if ($user['is_admin'] != 1) {
            return redirect()->back()->withInput()->with('error', 'Access denied. Admin privileges required.');
        }

        // Check if user is active
        if ($user['status'] != 1) {
            return redirect()->back()->withInput()->with('error', 'Your account has been deactivated. Please contact the administrator.');
        }

        // Set session data
        $sessionData = [
            'logged_in' => true,
            'emp_id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'role' => $user['role'],
            'is_admin' => $user['is_admin'],
            'is_supervisor' => $user['is_supervisor'],
            'org_id' => $user['org_id'],
            'sys_no' => $user['sys_no'],
            'position' => $user['position'],
            'id_photo' => $user['id_photo']
        ];

        // Get organization details
        $orgModel = new \App\Models\DakoiiOrgModel();
        $org = $orgModel->find($user['org_id']);
        
        if ($org) {
            $sessionData['orgcode'] = $org['orgcode'];
            $sessionData['orgname'] = $org['name'];
            $sessionData['orglogo'] = $org['orglogo'];
            $sessionData['orgprovince_id'] = $org['addlockprov'];
            $sessionData['orgcountry_id'] = $org['addlockcountry'];
        }

        // Get user's default district
        $permissionsModel = new \App\Models\PermissionsUserDistrictsModel();
        $defaultDistrict = $permissionsModel->where('user_id', $user['id'])
                                           ->where('default_district', 1)
                                           ->first();
        
        if ($defaultDistrict) {
            $sessionData['district_id'] = $defaultDistrict['district_id'];
            
            // Get district name
            $districtModel = new \App\Models\AdxDistrictModel();
            $district = $districtModel->find($defaultDistrict['district_id']);
            if ($district) {
                $sessionData['district_name'] = $district['name'];
            }
        }

        $this->session->set($sessionData);

        // Log successful login
        log_message('info', 'Admin login successful: ' . $user['name'] . ' (ID: ' . $user['id'] . ')');

        return redirect()->to('admin/dashboard')->with('success', 'Welcome to the Admin Portal, ' . $user['name']);
    }

    /**
     * Logout admin user
     */
    public function logout()
    {
        $userName = $this->session->get('name');
        
        // Destroy session
        $this->session->destroy();
        
        // Log logout
        log_message('info', 'Admin logout: ' . $userName);

        return redirect()->to('admin/login')->with('success', 'You have been logged out successfully');
    }
}
