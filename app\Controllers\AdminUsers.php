<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UsersModel;
use App\Models\PermissionsUserDistrictsModel;
use App\Models\PermissionsItemsModel;
use App\Models\PermissionsSetsModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxProvinceModel;
use App\Models\DakoiiOrgModel;

class AdminUsers extends BaseController
{
    protected $usersModel;
    protected $permissionsUserDistrictsModel;
    protected $permissionsItemsModel;
    protected $permissionsSetsModel;
    protected $districtModel;
    protected $provinceModel;
    protected $orgModel;
    protected $helpers = ['form', 'url', 'info'];

    public function __construct()
    {
        $this->usersModel = new UsersModel();
        $this->permissionsUserDistrictsModel = new PermissionsUserDistrictsModel();
        $this->permissionsItemsModel = new PermissionsItemsModel();
        $this->permissionsSetsModel = new PermissionsSetsModel();
        $this->districtModel = new AdxDistrictModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->orgModel = new DakoiiOrgModel();
    }

    /**
     * Display list of organization users
     */
    public function index()
    {
        $orgId = session()->get('org_id');
        
        // Get users with their district permissions and permission counts
        $users = $this->usersModel->select('users.*, 
                                          COUNT(DISTINCT pud.district_id) as district_count,
                                          COUNT(DISTINCT ps.permission_id) as permission_count')
                                  ->join('permissions_user_districts pud', 'pud.user_id = users.id', 'left')
                                  ->join('permissions_sets ps', 'ps.user_id = users.id', 'left')
                                  ->where('users.org_id', $orgId)
                                  ->groupBy('users.id')
                                  ->orderBy('users.name', 'ASC')
                                  ->findAll();

        $data = [
            'title' => 'User Management',
            'page_header' => 'Organization Users',
            'page_desc' => 'Manage users and their permissions',
            'menu' => 'admin-users',
            'users' => $users
        ];

        return view('admin/users/admin_users_index', $data);
    }

    /**
     * Show create user form
     */
    public function create()
    {
        $orgId = session()->get('org_id');
        $provinceId = session()->get('orgprovince_id');
        
        $data = [
            'title' => 'Create User',
            'page_header' => 'Create New User',
            'page_desc' => 'Add a new user to the organization',
            'menu' => 'admin-users',
            'districts' => $this->districtModel->where('province_id', $provinceId)->findAll(),
            'permissions' => $this->permissionsItemsModel->orderBy('permission_text', 'ASC')->findAll()
        ];

        return view('admin/users/admin_users_create', $data);
    }

    /**
     * Store new user
     */
    public function store()
    {
        $orgId = session()->get('org_id');
        $provinceId = session()->get('orgprovince_id');
        
        // Validation rules
        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'email' => 'permit_empty|valid_email|max_length[255]',
            'phone' => 'permit_empty|max_length[20]',
            'password' => 'required|min_length[6]',
            'role' => 'required|in_list[user,guest]',
            'position' => 'permit_empty|max_length[255]',
            'districts' => 'required',
            'default_district' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'org_id' => $orgId,
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'phone' => $this->request->getPost('phone'),
            'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
            'role' => $this->request->getPost('role'),
            'position' => $this->request->getPost('position'),
            'is_admin' => $this->request->getPost('is_admin') ? 1 : 0,
            'is_supervisor' => $this->request->getPost('is_supervisor') ? 1 : 0,
            'status' => 1,
            'created_by' => session()->get('emp_id')
        ];

        // Handle photo upload
        $photo = $this->request->getFile('id_photo');
        if ($photo && $photo->isValid() && !$photo->hasMoved()) {
            $newName = $photo->getRandomName();
            $photo->move(FCPATH . 'public/uploads/user_photos', $newName);
            $data['id_photo'] = 'public/uploads/user_photos/' . $newName;
        }

        $userId = $this->usersModel->insert($data);

        if ($userId) {
            // Add district permissions
            $districts = $this->request->getPost('districts');
            $defaultDistrict = $this->request->getPost('default_district');
            
            foreach ($districts as $districtId) {
                $this->permissionsUserDistrictsModel->insert([
                    'org_id' => $orgId,
                    'user_id' => $userId,
                    'district_id' => $districtId,
                    'default_district' => ($districtId == $defaultDistrict) ? 1 : 0,
                    'created_by' => session()->get('emp_id')
                ]);
            }

            // Add permissions
            $permissions = $this->request->getPost('permissions');
            if ($permissions) {
                foreach ($permissions as $permissionId) {
                    $this->permissionsSetsModel->insert([
                        'permission_id' => $permissionId,
                        'user_id' => $userId,
                        'created_by' => session()->get('emp_id')
                    ]);
                }
            }

            return redirect()->to('admin/users')->with('success', 'User created successfully');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to create user');
    }

    /**
     * Show user details
     */
    public function show($id)
    {
        $orgId = session()->get('org_id');
        
        $user = $this->usersModel->where('id', $id)->where('org_id', $orgId)->first();
        
        if (!$user) {
            return redirect()->to('admin/users')->with('error', 'User not found');
        }

        // Get user's district permissions
        $userDistricts = $this->permissionsUserDistrictsModel->getUserDistricts($id, $orgId);
        
        // Get user's permissions
        $userPermissions = $this->permissionsSetsModel->getUserPermissions($id);

        $data = [
            'title' => 'User Details',
            'page_header' => 'User Details: ' . $user['name'],
            'page_desc' => 'View user information and permissions',
            'menu' => 'admin-users',
            'user' => $user,
            'userDistricts' => $userDistricts,
            'userPermissions' => $userPermissions
        ];

        return view('admin/users/admin_users_show', $data);
    }

    /**
     * Show edit user form
     */
    public function edit($id)
    {
        $orgId = session()->get('org_id');
        $provinceId = session()->get('orgprovince_id');
        
        $user = $this->usersModel->where('id', $id)->where('org_id', $orgId)->first();
        
        if (!$user) {
            return redirect()->to('admin/users')->with('error', 'User not found');
        }

        // Get user's current districts and permissions
        $userDistricts = $this->permissionsUserDistrictsModel->getUserDistricts($id, $orgId);
        $userPermissions = $this->permissionsSetsModel->getUserPermissions($id);
        
        $data = [
            'title' => 'Edit User',
            'page_header' => 'Edit User: ' . $user['name'],
            'page_desc' => 'Update user information and permissions',
            'menu' => 'admin-users',
            'user' => $user,
            'districts' => $this->districtModel->where('province_id', $provinceId)->findAll(),
            'permissions' => $this->permissionsItemsModel->orderBy('permission_text', 'ASC')->findAll(),
            'userDistricts' => $userDistricts,
            'userPermissions' => $userPermissions
        ];

        return view('admin/users/admin_users_edit', $data);
    }

    /**
     * Update user
     */
    public function update($id)
    {
        $orgId = session()->get('org_id');
        
        $user = $this->usersModel->where('id', $id)->where('org_id', $orgId)->first();
        
        if (!$user) {
            return redirect()->to('admin/users')->with('error', 'User not found');
        }

        // Validation rules
        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'email' => 'permit_empty|valid_email|max_length[255]',
            'phone' => 'permit_empty|max_length[20]',
            'role' => 'required|in_list[user,guest]',
            'position' => 'permit_empty|max_length[255]',
            'districts' => 'required',
            'default_district' => 'required'
        ];

        // Only validate password if provided
        if ($this->request->getPost('password')) {
            $rules['password'] = 'min_length[6]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'phone' => $this->request->getPost('phone'),
            'role' => $this->request->getPost('role'),
            'position' => $this->request->getPost('position'),
            'is_admin' => $this->request->getPost('is_admin') ? 1 : 0,
            'is_supervisor' => $this->request->getPost('is_supervisor') ? 1 : 0,
            'status' => $this->request->getPost('status'),
            'updated_by' => session()->get('emp_id')
        ];

        // Update password if provided
        if ($this->request->getPost('password')) {
            $data['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
        }

        // Handle photo upload
        $photo = $this->request->getFile('id_photo');
        if ($photo && $photo->isValid() && !$photo->hasMoved()) {
            // Delete old photo if exists
            if ($user['id_photo'] && file_exists(FCPATH . $user['id_photo'])) {
                unlink(FCPATH . $user['id_photo']);
            }
            
            $newName = $photo->getRandomName();
            $photo->move(FCPATH . 'public/uploads/user_photos', $newName);
            $data['id_photo'] = 'public/uploads/user_photos/' . $newName;
        }

        if ($this->usersModel->update($id, $data)) {
            // Update district permissions
            $this->permissionsUserDistrictsModel->where('user_id', $id)->delete();
            
            $districts = $this->request->getPost('districts');
            $defaultDistrict = $this->request->getPost('default_district');
            
            foreach ($districts as $districtId) {
                $this->permissionsUserDistrictsModel->insert([
                    'org_id' => $orgId,
                    'user_id' => $id,
                    'district_id' => $districtId,
                    'default_district' => ($districtId == $defaultDistrict) ? 1 : 0,
                    'created_by' => session()->get('emp_id')
                ]);
            }

            // Update permissions
            $this->permissionsSetsModel->removeAllUserPermissions($id);
            
            $permissions = $this->request->getPost('permissions');
            if ($permissions) {
                foreach ($permissions as $permissionId) {
                    $this->permissionsSetsModel->addUserPermission($id, $permissionId, session()->get('emp_id'));
                }
            }

            return redirect()->to('admin/users')->with('success', 'User updated successfully');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to update user');
    }

    /**
     * Delete user
     */
    public function delete($id)
    {
        $orgId = session()->get('org_id');
        
        $user = $this->usersModel->where('id', $id)->where('org_id', $orgId)->first();
        
        if (!$user) {
            return redirect()->to('admin/users')->with('error', 'User not found');
        }

        // Don't allow deletion of current user
        if ($id == session()->get('emp_id')) {
            return redirect()->to('admin/users')->with('error', 'Cannot delete your own account');
        }

        // Delete user's permissions and district assignments
        $this->permissionsSetsModel->removeAllUserPermissions($id);
        $this->permissionsUserDistrictsModel->where('user_id', $id)->delete();
        
        // Delete user photo if exists
        if ($user['id_photo'] && file_exists(FCPATH . $user['id_photo'])) {
            unlink(FCPATH . $user['id_photo']);
        }

        if ($this->usersModel->delete($id)) {
            return redirect()->to('admin/users')->with('success', 'User deleted successfully');
        }

        return redirect()->to('admin/users')->with('error', 'Failed to delete user');
    }

    /**
     * Handle form-based update (for compatibility)
     */
    public function update_form($id)
    {
        return $this->update($id);
    }

    /**
     * Handle form-based delete (for compatibility)
     */
    public function destroy_form($id)
    {
        return $this->delete($id);
    }

    /**
     * Delete user (alias for destroy)
     */
    public function destroy($id)
    {
        return $this->delete($id);
    }
}
