<?php

namespace App\Controllers;

use App\Models\AdxCountryModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;
use App\Models\GovStructureModel;

class DakoiiLocations extends BaseController
{
    public $session;
    public $countryModel;
    public $provinceModel;
    public $districtModel;
    public $llgModel;
    public $wardModel;
    public $govStructureModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        
        // Initialize models
        $this->countryModel = new AdxCountryModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->districtModel = new AdxDistrictModel();
        $this->llgModel = new AdxLlgModel();
        $this->wardModel = new AdxWardModel();
        $this->govStructureModel = new GovStructureModel();
    }

    /**
     * Display locations overview
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Locations Management";
        $data['menu'] = "locations";
        
        // Get location statistics
        $data['countries'] = $this->countryModel->findAll();
        $data['provinces'] = $this->provinceModel->findAll();
        $data['provinces_count'] = $this->provinceModel->countAllResults();
        $data['districts_count'] = $this->districtModel->countAllResults();
        $data['llgs_count'] = $this->llgModel->countAllResults();
        $data['wards_count'] = $this->wardModel->countAllResults();

        // Get province statistics
        $data['province_stats'] = $this->getProvinceStats();

        return view('dakoii/dakoii_locations_index', $data);
    }

    /**
     * Display provinces
     */
    public function provinces()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Provinces";
        $data['menu'] = "locations";
        $data['provinces'] = $this->provinceModel->findAll();
        $data['countries'] = $this->countryModel->findAll();

        // Get statistics for each province
        foreach ($data['provinces'] as &$province) {
            $province['districts_count'] = $this->districtModel->where('province_id', $province['id'])->countAllResults();
            $province['llgs_count'] = $this->llgModel->where('province_id', $province['id'])->countAllResults();
            $province['wards_count'] = $this->wardModel->where('province_id', $province['id'])->countAllResults();
        }

        return view('dakoii/dakoii_locations_provinces', $data);
    }

    /**
     * Display districts for a province
     */
    public function districts($provinceId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $province = $this->provinceModel->find($provinceId);
        if (!$province) {
            session()->setFlashdata('error', 'Province not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $data['title'] = "Districts - " . $province['name'];
        $data['menu'] = "locations";
        $data['province'] = $province;
        $data['districts'] = $this->districtModel->where('province_id', $provinceId)->findAll();

        // Get statistics for each district
        foreach ($data['districts'] as &$district) {
            $district['llgs_count'] = $this->llgModel->where('district_id', $district['id'])->countAllResults();
            $district['wards_count'] = $this->wardModel->where('district_id', $district['id'])->countAllResults();
        }

        return view('dakoii/dakoii_locations_districts', $data);
    }

    /**
     * Display LLGs for a district
     */
    public function llgs($districtId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $district = $this->districtModel->find($districtId);
        if (!$district) {
            session()->setFlashdata('error', 'District not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $province = $this->provinceModel->find($district['province_id']);

        $data['title'] = "LLGs - " . $district['name'];
        $data['menu'] = "locations";
        $data['district'] = $district;
        $data['province'] = $province;
        $data['llgs'] = $this->llgModel->where('district_id', $districtId)->findAll();

        // Get statistics for each LLG
        foreach ($data['llgs'] as &$llg) {
            $llg['wards_count'] = $this->wardModel->where('llg_id', $llg['id'])->countAllResults();
        }

        return view('dakoii/dakoii_locations_llgs', $data);
    }

    /**
     * Display wards for an LLG
     */
    public function wards($llgId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $llg = $this->llgModel->find($llgId);
        if (!$llg) {
            session()->setFlashdata('error', 'LLG not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $district = $this->districtModel->find($llg['district_id']);
        $province = $this->provinceModel->find($llg['province_id']);

        $data['title'] = "Wards - " . $llg['name'];
        $data['menu'] = "locations";
        $data['llg'] = $llg;
        $data['district'] = $district;
        $data['province'] = $province;
        $data['wards'] = $this->wardModel->where('llg_id', $llgId)->findAll();

        return view('dakoii/dakoii_locations_wards', $data);
    }

    // ========== WARD CRUD METHODS ==========

    /**
     * Show create ward form
     */
    public function createWard($llgId = null)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Add New Ward";
        $data['menu'] = "locations";
        $data['set_country'] = $this->countryModel->where('code', COUNTRY_CODE)->first();
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $data['provinces'] = $this->provinceModel->orderBy('name', 'ASC')->findAll();
        $data['districts'] = $this->districtModel->orderBy('name', 'ASC')->findAll();
        $data['llgs'] = $this->llgModel->orderBy('name', 'ASC')->findAll();
        $data['selected_llg_id'] = $llgId;

        // If LLG ID is provided, fetch LLG, district, and province info for back navigation
        if ($llgId) {
            $data['llg'] = $this->llgModel->find($llgId);
            if ($data['llg']) {
                $data['district'] = $this->districtModel->find($data['llg']['district_id']);
                if ($data['district']) {
                    $data['province'] = $this->provinceModel->find($data['district']['province_id']);
                }
            }
        }

        return view('dakoii/dakoii_locations_ward_create', $data);
    }

    /**
     * Store new ward
     */
    public function storeWard()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[100]',
            'wardcode' => 'required|min_length[2]|max_length[10]|is_unique[wards.wardcode]',
            'llg_id' => 'required|integer',
            'district_id' => 'required|integer',
            'province_id' => 'required|integer',
            'country_id' => 'required|integer'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            return redirect()->back()->withInput();
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'wardcode' => strtoupper($this->request->getPost('wardcode')),
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'district_id' => $this->request->getPost('district_id'),
            'llg_id' => $this->request->getPost('llg_id'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->wardModel->insert($data)) {
            session()->setFlashdata('success', 'Ward added successfully');
            $llgId = $this->request->getPost('llg_id');
            return redirect()->to("dakoii/locations/wards/{$llgId}");
        } else {
            session()->setFlashdata('error', 'Failed to add ward');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Show edit ward form
     */
    public function editWard($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $ward = $this->wardModel->find($id);
        if (!$ward) {
            session()->setFlashdata('error', 'Ward not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $data['title'] = "Edit Ward - " . $ward['name'];
        $data['menu'] = "locations";
        $data['ward'] = $ward;
        $data['llg'] = $this->llgModel->find($ward['llg_id']);
        $data['district'] = $this->districtModel->find($ward['district_id']);
        $data['province'] = $this->provinceModel->find($ward['province_id']);
        $data['set_country'] = $this->countryModel->find($ward['country_id']);
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $data['provinces'] = $this->provinceModel->orderBy('name', 'ASC')->findAll();
        $data['districts'] = $this->districtModel->orderBy('name', 'ASC')->findAll();
        $data['llgs'] = $this->llgModel->orderBy('name', 'ASC')->findAll();

        return view('dakoii/dakoii_locations_ward_edit', $data);
    }

    /**
     * Update ward
     */
    public function updateWard($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $ward = $this->wardModel->find($id);
        if (!$ward) {
            session()->setFlashdata('error', 'Ward not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[100]',
            'wardcode' => "required|min_length[2]|max_length[10]|is_unique[wards.wardcode,id,{$id}]",
            'llg_id' => 'required|integer',
            'district_id' => 'required|integer',
            'province_id' => 'required|integer',
            'country_id' => 'required|integer'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            return redirect()->back()->withInput();
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'wardcode' => strtoupper($this->request->getPost('wardcode')),
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'district_id' => $this->request->getPost('district_id'),
            'llg_id' => $this->request->getPost('llg_id'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->wardModel->update($id, $data)) {
            session()->setFlashdata('success', 'Ward updated successfully');
            $llgId = $this->request->getPost('llg_id');
            return redirect()->to("dakoii/locations/wards/{$llgId}");
        } else {
            session()->setFlashdata('error', 'Failed to update ward');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Delete ward
     */
    public function deleteWard($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $ward = $this->wardModel->find($id);
        if (!$ward) {
            session()->setFlashdata('error', 'Ward not found');
            return redirect()->back();
        }

        if ($this->wardModel->delete($id)) {
            session()->setFlashdata('success', 'Ward deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete ward');
        }

        return redirect()->to("dakoii/locations/wards/{$ward['llg_id']}");
    }

    /**
     * Get province statistics
     */
    private function getProvinceStats()
    {
        $provinces = $this->provinceModel->findAll();
        $stats = [];

        foreach ($provinces as $province) {
            $districts = $this->districtModel->where('province_id', $province['id'])->countAllResults();
            $llgs = $this->llgModel->where('province_id', $province['id'])->countAllResults();
            $wards = $this->wardModel->where('province_id', $province['id'])->countAllResults();

            $stats[] = [
                'province' => $province,
                'districts_count' => $districts,
                'llgs_count' => $llgs,
                'wards_count' => $wards,
                'total_locations' => $districts + $llgs + $wards
            ];
        }

        // Sort by total locations descending
        usort($stats, function($a, $b) {
            return $b['total_locations'] - $a['total_locations'];
        });

        return $stats;
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }

    /**
     * Get current user information
     */
    private function getCurrentUser()
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return [
            'id' => $this->session->get('dakoii_user_id'),
            'name' => $this->session->get('dakoii_name'),
            'username' => $this->session->get('dakoii_username'),
            'role' => $this->session->get('dakoii_role'),
            'orgcode' => $this->session->get('dakoii_orgcode')
        ];
    }

    // ========== COUNTRY CRUD METHODS ==========

    /**
     * Display countries list
     */
    public function countries()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Countries Management";
        $data['menu'] = "locations";
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();

        return view('dakoii/dakoii_locations_countries', $data);
    }

    /**
     * Show create country form
     */
    public function createCountry()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Add New Country";
        $data['menu'] = "locations";

        return view('dakoii/dakoii_locations_country_create', $data);
    }

    /**
     * Store new country
     */
    public function storeCountry()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'code' => 'required|min_length[2]|max_length[10]|is_unique[adx_country.code]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'code' => strtoupper($this->request->getPost('code')),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->countryModel->insert($data)) {
            session()->setFlashdata('success', 'Country added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add country');
        }

        return redirect()->to('dakoii/locations/countries');
    }

    /**
     * Show edit country form
     */
    public function editCountry($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $country = $this->countryModel->find($id);
        if (!$country) {
            session()->setFlashdata('error', 'Country not found');
            return redirect()->to('dakoii/locations/countries');
        }

        $data['title'] = "Edit Country - " . $country['name'];
        $data['menu'] = "locations";
        $data['country'] = $country;

        return view('dakoii/dakoii_locations_country_edit', $data);
    }

    /**
     * Update country
     */
    public function updateCountry($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $country = $this->countryModel->find($id);
        if (!$country) {
            session()->setFlashdata('error', 'Country not found');
            return redirect()->to('dakoii/locations/countries');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'code' => "required|min_length[2]|max_length[10]|is_unique[adx_country.code,id,{$id}]"
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'code' => strtoupper($this->request->getPost('code')),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->countryModel->update($id, $data)) {
            session()->setFlashdata('success', 'Country updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update country');
        }

        return redirect()->to('dakoii/locations/countries');
    }

    /**
     * Delete country
     */
    public function deleteCountry($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $country = $this->countryModel->find($id);
        if (!$country) {
            session()->setFlashdata('error', 'Country not found');
            return redirect()->to('dakoii/locations/countries');
        }

        // Check if country has provinces
        $provinceCount = $this->provinceModel->where('country_id', $id)->countAllResults();
        if ($provinceCount > 0) {
            session()->setFlashdata('error', 'Cannot delete country that has provinces');
            return redirect()->to('dakoii/locations/countries');
        }

        if ($this->countryModel->delete($id)) {
            session()->setFlashdata('success', 'Country deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete country');
        }

        return redirect()->to('dakoii/locations/countries');
    }

    // ========== PROVINCE CRUD METHODS ==========

    /**
     * Show create province form
     */
    public function createProvince()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Add New Province";
        $data['menu'] = "locations";
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();

        return view('dakoii/provinces_create', $data);
    }

    /**
     * Store new province
     */
    public function storeProvince()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'provincecode' => 'required|min_length[2]|max_length[20]|is_unique[adx_province.provincecode]',
            'country_id' => 'required|integer',
            'json_id' => 'required|max_length[255]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'provincecode' => strtoupper($this->request->getPost('provincecode')),
            'country_id' => $this->request->getPost('country_id'),
            'json_id' => $this->request->getPost('json_id'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->provinceModel->insert($data)) {
            session()->setFlashdata('success', 'Province added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add province');
        }

        return redirect()->to('dakoii/locations/provinces');
    }

    /**
     * Show edit province form
     */
    public function editProvince($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $province = $this->provinceModel->find($id);
        if (!$province) {
            session()->setFlashdata('error', 'Province not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $data['title'] = "Edit Province - " . $province['name'];
        $data['menu'] = "locations";
        $data['province'] = $province;
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();

        return view('dakoii/dakoii_locations_province_edit', $data);
    }

    /**
     * Update province
     */
    public function updateProvince($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $province = $this->provinceModel->find($id);
        if (!$province) {
            session()->setFlashdata('error', 'Province not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'provincecode' => "required|min_length[2]|max_length[20]|is_unique[adx_province.provincecode,id,{$id}]",
            'country_id' => 'required|integer',
            'json_id' => 'required|max_length[255]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'provincecode' => strtoupper($this->request->getPost('provincecode')),
            'country_id' => $this->request->getPost('country_id'),
            'json_id' => $this->request->getPost('json_id'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->provinceModel->update($id, $data)) {
            session()->setFlashdata('success', 'Province updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update province');
        }

        return redirect()->to('dakoii/locations/provinces');
    }

    /**
     * Delete province
     */
    public function deleteProvince($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $province = $this->provinceModel->find($id);
        if (!$province) {
            session()->setFlashdata('error', 'Province not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        // Check if province has districts
        $districtCount = $this->districtModel->where('province_id', $id)->countAllResults();
        if ($districtCount > 0) {
            session()->setFlashdata('error', 'Cannot delete province that has districts');
            return redirect()->to('dakoii/locations/provinces');
        }

        if ($this->provinceModel->delete($id)) {
            session()->setFlashdata('success', 'Province deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete province');
        }

        return redirect()->to('dakoii/locations/provinces');
    }

    // ========== DISTRICT CRUD METHODS ==========

    /**
     * Show create district form
     */
    public function createDistrict($provinceId = null)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Add New District";
        $data['menu'] = "locations";
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $data['provinces'] = $this->provinceModel->orderBy('name', 'ASC')->findAll();
        $data['selected_province_id'] = $provinceId;

        return view('dakoii/dakoii_locations_district_create', $data);
    }

    /**
     * Store new district
     */
    public function storeDistrict()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'districtcode' => 'required|min_length[2]|max_length[20]|is_unique[adx_district.districtcode]',
            'country_id' => 'required|integer',
            'province_id' => 'required|integer',
            'json_id' => 'required|max_length[255]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'districtcode' => strtoupper($this->request->getPost('districtcode')),
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'json_id' => $this->request->getPost('json_id'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->districtModel->insert($data)) {
            session()->setFlashdata('success', 'District added successfully');
            $provinceId = $this->request->getPost('province_id');
            return redirect()->to("dakoii/locations/districts/{$provinceId}");
        } else {
            session()->setFlashdata('error', 'Failed to add district');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Show edit district form
     */
    public function editDistrict($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $district = $this->districtModel->find($id);
        if (!$district) {
            session()->setFlashdata('error', 'District not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $data['title'] = "Edit District - " . $district['name'];
        $data['menu'] = "locations";
        $data['district'] = $district;
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $data['provinces'] = $this->provinceModel->orderBy('name', 'ASC')->findAll();

        return view('dakoii/dakoii_locations_district_edit', $data);
    }

    /**
     * Update district
     */
    public function updateDistrict($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $district = $this->districtModel->find($id);
        if (!$district) {
            session()->setFlashdata('error', 'District not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'districtcode' => "required|min_length[2]|max_length[20]|is_unique[adx_district.districtcode,id,{$id}]",
            'country_id' => 'required|integer',
            'province_id' => 'required|integer',
            'json_id' => 'required|max_length[255]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'districtcode' => strtoupper($this->request->getPost('districtcode')),
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'json_id' => $this->request->getPost('json_id'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->districtModel->update($id, $data)) {
            session()->setFlashdata('success', 'District updated successfully');
            $provinceId = $this->request->getPost('province_id');
            return redirect()->to("dakoii/locations/districts/{$provinceId}");
        } else {
            session()->setFlashdata('error', 'Failed to update district');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Delete district
     */
    public function deleteDistrict($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $district = $this->districtModel->find($id);
        if (!$district) {
            session()->setFlashdata('error', 'District not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        // Check if district has LLGs
        $llgCount = $this->llgModel->where('district_id', $id)->countAllResults();
        if ($llgCount > 0) {
            session()->setFlashdata('error', 'Cannot delete district that has LLGs');
            $provinceId = $district['province_id'];
            return redirect()->to("dakoii/locations/districts/{$provinceId}");
        }

        $provinceId = $district['province_id'];
        if ($this->districtModel->delete($id)) {
            session()->setFlashdata('success', 'District deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete district');
        }

        return redirect()->to("dakoii/locations/districts/{$provinceId}");
    }

    // ========== LLG CRUD METHODS ==========

    /**
     * Show create LLG form
     */
    public function createLlg($districtId = null)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Add New LLG";
        $data['menu'] = "locations";
        $data['set_country'] = $this->countryModel->where('code', COUNTRY_CODE)->first();
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $data['provinces'] = $this->provinceModel->orderBy('name', 'ASC')->findAll();
        $data['districts'] = $this->districtModel->orderBy('name', 'ASC')->findAll();
        $data['selected_district_id'] = $districtId;

        // If district ID is provided, fetch district and province info for back navigation
        if ($districtId) {
            $data['district'] = $this->districtModel->find($districtId);
            if ($data['district']) {
                $data['province'] = $this->provinceModel->find($data['district']['province_id']);
            }
        }

        return view('dakoii/dakoii_locations_llg_create', $data);
    }

    /**
     * Store new LLG
     */
    public function storeLlg()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'llgcode' => 'required|min_length[2]|max_length[20]|is_unique[adx_llg.llgcode]',
            'country_id' => 'required|integer',
            'province_id' => 'required|integer',
            'district_id' => 'required|integer'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'llgcode' => strtoupper($this->request->getPost('llgcode')),
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'district_id' => $this->request->getPost('district_id'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->llgModel->insert($data)) {
            session()->setFlashdata('success', 'LLG added successfully');
            $districtId = $this->request->getPost('district_id');
            return redirect()->to("dakoii/locations/llgs/{$districtId}");
        } else {
            session()->setFlashdata('error', 'Failed to add LLG');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Show edit LLG form
     */
    public function editLlg($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $llg = $this->llgModel->find($id);
        if (!$llg) {
            session()->setFlashdata('error', 'LLG not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $data['title'] = "Edit LLG - " . $llg['name'];
        $data['menu'] = "locations";
        $data['llg'] = $llg;
        $data['district'] = $this->districtModel->find($llg['district_id']);
        $data['province'] = $this->provinceModel->find($llg['province_id']);
        $data['set_country'] = $this->countryModel->find($llg['country_id']);
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $data['provinces'] = $this->provinceModel->orderBy('name', 'ASC')->findAll();
        $data['districts'] = $this->districtModel->orderBy('name', 'ASC')->findAll();

        return view('dakoii/dakoii_locations_llg_edit', $data);
    }

    /**
     * Update LLG
     */
    public function updateLlg($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $llg = $this->llgModel->find($id);
        if (!$llg) {
            session()->setFlashdata('error', 'LLG not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'llgcode' => "required|min_length[2]|max_length[20]|is_unique[adx_llg.llgcode,id,{$id}]",
            'country_id' => 'required|integer',
            'province_id' => 'required|integer',
            'district_id' => 'required|integer'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'llgcode' => strtoupper($this->request->getPost('llgcode')),
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'district_id' => $this->request->getPost('district_id'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->llgModel->update($id, $data)) {
            session()->setFlashdata('success', 'LLG updated successfully');
            $districtId = $this->request->getPost('district_id');
            return redirect()->to("dakoii/locations/llgs/{$districtId}");
        } else {
            session()->setFlashdata('error', 'Failed to update LLG');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Delete LLG
     */
    public function deleteLlg($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $llg = $this->llgModel->find($id);
        if (!$llg) {
            session()->setFlashdata('error', 'LLG not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        // Check if LLG has wards
        $wardCount = $this->wardModel->where('llg_id', $id)->countAllResults();
        if ($wardCount > 0) {
            session()->setFlashdata('error', 'Cannot delete LLG that has wards');
            $districtId = $llg['district_id'];
            return redirect()->to("dakoii/locations/llgs/{$districtId}");
        }

        $districtId = $llg['district_id'];
        if ($this->llgModel->delete($id)) {
            session()->setFlashdata('success', 'LLG deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete LLG');
        }

        return redirect()->to("dakoii/locations/llgs/{$districtId}");
    }

    // ========================================
    // CSV IMPORT FUNCTIONALITY
    // ========================================

    /**
     * Show province CSV import form (GET)
     */
    public function importProvincesForm()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Import Provinces from CSV";
        $data['menu'] = "locations";

        return view('dakoii/dakoii_locations_import_provinces', $data);
    }

    /**
     * Process province CSV import (POST)
     */
    public function importProvinces()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/locations/provinces/import');
        }

        // Validate file upload
        $rules = [
            'csv_file' => 'uploaded[csv_file]|max_size[csv_file,2048]|ext_in[csv_file,csv]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please upload a valid CSV file (max 2MB)');
            return redirect()->to('dakoii/locations/provinces/import')->withInput();
        }

        $file = $this->request->getFile('csv_file');

        if (!$file->isValid()) {
            session()->setFlashdata('error', 'Invalid file upload');
            return redirect()->to('dakoii/locations/provinces/import');
        }

        // Get country ID - try PNG first, then any available country
        $country = $this->countryModel->where('code', 'PNG')->first();
        if (!$country) {
            // If PNG not found, get the first available country
            $country = $this->countryModel->orderBy('id', 'ASC')->first();
        }
        if (!$country) {
            session()->setFlashdata('error', 'No countries found in the system. Please contact administrator.');
            return redirect()->to('dakoii/locations/provinces/import');
        }

        $countryId = $country['id'];
        $results = [
            'total' => 0,
            'imported' => 0,
            'skipped' => 0,
            'errors' => []
        ];

        // Parse CSV file
        $handle = fopen($file->getTempName(), 'r');
        $header = fgetcsv($handle); // Skip header row

        // Validate header
        if (!$header || count($header) < 2 ||
            strtolower(trim($header[0])) !== 'code' ||
            strtolower(trim($header[1])) !== 'name') {
            fclose($handle);
            session()->setFlashdata('error', 'Invalid CSV format. Expected columns: code, name');
            return redirect()->to('dakoii/locations/provinces/import');
        }

        $db = \Config\Database::connect();
        $db->transStart();

        $lineNumber = 1;
        while (($data = fgetcsv($handle)) !== FALSE) {
            $lineNumber++;
            $results['total']++;

            if (count($data) < 2) {
                $results['errors'][] = "Line {$lineNumber}: Insufficient data";
                continue;
            }

            $provincecode = trim($data[0]);
            $name = trim($data[1]);

            if (empty($provincecode) || empty($name)) {
                $results['errors'][] = "Line {$lineNumber}: Code and name are required";
                continue;
            }

            // Check if province already exists
            $existing = $this->provinceModel->where('provincecode', $provincecode)->first();
            if ($existing) {
                $results['skipped']++;
                $results['errors'][] = "Line {$lineNumber}: Province code '{$provincecode}' already exists";
                continue;
            }

            // Insert province
            $provinceData = [
                'provincecode' => $provincecode,
                'name' => $name,
                'country_id' => $countryId,
                'json_id' => $provincecode // Use code as json_id
            ];

            if ($this->provinceModel->insert($provinceData)) {
                $results['imported']++;
            } else {
                $results['errors'][] = "Line {$lineNumber}: Failed to insert province '{$name}'";
            }
        }

        fclose($handle);
        $db->transComplete();

        if ($db->transStatus() === FALSE) {
            session()->setFlashdata('error', 'Import failed due to database error');
            return redirect()->to('dakoii/locations/provinces/import');
        }

        // Set success message with results
        $message = "Import completed! Imported: {$results['imported']}, Skipped: {$results['skipped']}, Total: {$results['total']}";
        if (!empty($results['errors'])) {
            $message .= "\n\nErrors:\n" . implode("\n", array_slice($results['errors'], 0, 10));
            if (count($results['errors']) > 10) {
                $message .= "\n... and " . (count($results['errors']) - 10) . " more errors";
            }
        }

        session()->setFlashdata('success', $message);
        return redirect()->to('dakoii/locations/provinces');
    }

    /**
     * Download province CSV sample file
     */
    public function downloadProvincesSample()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $filename = 'provinces_sample.csv';
        $content = "code,name\n";
        $content .= "1401,\"East Sepik\"\n";
        $content .= "1402,\"Western Province\"\n";
        $content .= "1403,\"Central Province\"\n";

        return $this->response
            ->setHeader('Content-Type', 'text/csv')
            ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->setBody($content);
    }

    /**
     * Show district CSV import form (GET)
     */
    public function importDistrictsForm($provinceId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Get province details
        $province = $this->provinceModel->find($provinceId);
        if (!$province) {
            session()->setFlashdata('error', 'Province not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $data['title'] = "Import Districts for " . $province['name'];
        $data['menu'] = "locations";
        $data['province'] = $province;

        return view('dakoii/dakoii_locations_import_districts', $data);
    }

    /**
     * Process district CSV import (POST)
     */
    public function importDistricts($provinceId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Get province details
        $province = $this->provinceModel->find($provinceId);
        if (!$province) {
            session()->setFlashdata('error', 'Province not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        // Validate file upload
        $rules = [
            'csv_file' => 'uploaded[csv_file]|max_size[csv_file,2048]|ext_in[csv_file,csv]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please upload a valid CSV file (max 2MB)');
            return redirect()->to("dakoii/locations/districts/import/{$provinceId}")->withInput();
        }

        $file = $this->request->getFile('csv_file');

        if (!$file->isValid()) {
            session()->setFlashdata('error', 'Invalid file upload');
            return redirect()->to("dakoii/locations/districts/import/{$provinceId}");
        }

        // Use the country from the province
        $countryId = $province['country_id'];
        $results = [
            'total' => 0,
            'imported' => 0,
            'skipped' => 0,
            'errors' => []
        ];

        // Parse CSV file
        $handle = fopen($file->getTempName(), 'r');
        $header = fgetcsv($handle); // Skip header row

        // Validate header - only need districtcode and name since we know the province
        if (!$header || count($header) < 2 ||
            strtolower(trim($header[0])) !== 'districtcode' ||
            strtolower(trim($header[1])) !== 'name') {
            fclose($handle);
            session()->setFlashdata('error', 'Invalid CSV format. Expected columns: districtcode, name');
            return redirect()->to("dakoii/locations/districts/import/{$provinceId}");
        }

        $db = \Config\Database::connect();
        $db->transStart();

        $lineNumber = 1;
        while (($data = fgetcsv($handle)) !== FALSE) {
            $lineNumber++;
            $results['total']++;

            if (count($data) < 2) {
                $results['errors'][] = "Line {$lineNumber}: Insufficient data";
                continue;
            }

            $districtcode = trim($data[0]);
            $name = trim($data[1]);

            if (empty($districtcode) || empty($name)) {
                $results['errors'][] = "Line {$lineNumber}: District code and name are required";
                continue;
            }

            // Check if district already exists
            $existing = $this->districtModel->where('districtcode', $districtcode)->first();
            if ($existing) {
                $results['skipped']++;
                $results['errors'][] = "Line {$lineNumber}: District code '{$districtcode}' already exists";
                continue;
            }

            // Insert district for the specific province
            $districtData = [
                'districtcode' => $districtcode,
                'name' => $name,
                'country_id' => $countryId,
                'province_id' => $provinceId, // Use the province ID from the URL
                'json_id' => $districtcode // Use code as json_id
            ];

            if ($this->districtModel->insert($districtData)) {
                $results['imported']++;
            } else {
                $results['errors'][] = "Line {$lineNumber}: Failed to insert district '{$name}'";
            }
        }

        fclose($handle);
        $db->transComplete();

        if ($db->transStatus() === FALSE) {
            session()->setFlashdata('error', 'Import failed due to database error');
            return redirect()->to("dakoii/locations/districts/import/{$provinceId}");
        }

        // Set success message with results
        $message = "Import completed! Imported: {$results['imported']}, Skipped: {$results['skipped']}, Total: {$results['total']}";
        if (!empty($results['errors'])) {
            $message .= "\n\nErrors:\n" . implode("\n", array_slice($results['errors'], 0, 10));
            if (count($results['errors']) > 10) {
                $message .= "\n... and " . (count($results['errors']) - 10) . " more errors";
            }
        }

        session()->setFlashdata('success', $message);
        return redirect()->to("dakoii/locations/districts/{$provinceId}");
    }

    /**
     * Download district CSV sample file
     */
    public function downloadDistrictsSample($provinceId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Get province details for sample
        $province = $this->provinceModel->find($provinceId);
        $provinceName = $province ? $province['name'] : 'Province';

        $filename = 'districts_sample_' . strtolower(str_replace(' ', '_', $provinceName)) . '.csv';
        $content = "districtcode,name\n";
        $content .= "1401,\"Ambunti/Drekikier\"\n";
        $content .= "1402,\"Angoram\"\n";
        $content .= "1403,\"Maprik\"\n";

        return $this->response
            ->setHeader('Content-Type', 'text/csv')
            ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->setBody($content);
    }

    /**
     * Show LLG CSV import form (GET)
     */
    public function importLlgsForm($districtId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Get district details
        $district = $this->districtModel->find($districtId);
        if (!$district) {
            session()->setFlashdata('error', 'District not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        // Get province details
        $province = $this->provinceModel->find($district['province_id']);

        $data['title'] = "Import LLGs for " . $district['name'];
        $data['menu'] = "locations";
        $data['district'] = $district;
        $data['province'] = $province;

        return view('dakoii/dakoii_locations_import_llgs', $data);
    }

    /**
     * Process LLG CSV import (POST)
     */
    public function importLlgs($districtId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Get district details
        $district = $this->districtModel->find($districtId);
        if (!$district) {
            session()->setFlashdata('error', 'District not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        // Validate file upload
        $rules = [
            'csv_file' => 'uploaded[csv_file]|max_size[csv_file,2048]|ext_in[csv_file,csv]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please upload a valid CSV file (max 2MB)');
            return redirect()->to("dakoii/locations/llgs/import/{$districtId}")->withInput();
        }

        $file = $this->request->getFile('csv_file');

        if (!$file->isValid()) {
            session()->setFlashdata('error', 'Invalid file upload');
            return redirect()->to("dakoii/locations/llgs/import/{$districtId}");
        }

        // Use the country from the district (which comes from province)
        $countryId = $district['country_id'];
        $results = [
            'total' => 0,
            'imported' => 0,
            'skipped' => 0,
            'errors' => []
        ];

        // Parse CSV file
        $handle = fopen($file->getTempName(), 'r');
        $header = fgetcsv($handle); // Skip header row

        // Validate header - only need llgcode and name since we know the district
        if (!$header || count($header) < 2 ||
            strtolower(trim($header[0])) !== 'llgcode' ||
            strtolower(trim($header[1])) !== 'name') {
            fclose($handle);
            session()->setFlashdata('error', 'Invalid CSV format. Expected columns: llgcode, name');
            return redirect()->to("dakoii/locations/llgs/import/{$districtId}");
        }

        $db = \Config\Database::connect();
        $db->transStart();

        $lineNumber = 1;
        while (($data = fgetcsv($handle)) !== FALSE) {
            $lineNumber++;
            $results['total']++;

            if (count($data) < 2) {
                $results['errors'][] = "Line {$lineNumber}: Insufficient data";
                continue;
            }

            $llgcode = trim($data[0]);
            $name = trim($data[1]);

            if (empty($llgcode) || empty($name)) {
                $results['errors'][] = "Line {$lineNumber}: LLG code and name are required";
                continue;
            }

            // Check if LLG already exists
            $existing = $this->llgModel->where('llgcode', $llgcode)->first();
            if ($existing) {
                $results['skipped']++;
                $results['errors'][] = "Line {$lineNumber}: LLG code '{$llgcode}' already exists";
                continue;
            }

            // Insert LLG for the specific district
            $llgData = [
                'llgcode' => $llgcode,
                'name' => $name,
                'country_id' => $countryId,
                'province_id' => $district['province_id'], // Use district's province
                'district_id' => $districtId, // Use the district ID from the URL
                'json_id' => $llgcode // Use code as json_id
            ];

            if ($this->llgModel->insert($llgData)) {
                $results['imported']++;
            } else {
                $results['errors'][] = "Line {$lineNumber}: Failed to insert LLG '{$name}'";
            }
        }

        fclose($handle);
        $db->transComplete();

        if ($db->transStatus() === FALSE) {
            session()->setFlashdata('error', 'Import failed due to database error');
            return redirect()->to("dakoii/locations/llgs/import/{$districtId}");
        }

        // Set success message with results
        $message = "Import completed! Imported: {$results['imported']}, Skipped: {$results['skipped']}, Total: {$results['total']}";
        if (!empty($results['errors'])) {
            $message .= "\n\nErrors:\n" . implode("\n", array_slice($results['errors'], 0, 10));
            if (count($results['errors']) > 10) {
                $message .= "\n... and " . (count($results['errors']) - 10) . " more errors";
            }
        }

        session()->setFlashdata('success', $message);
        return redirect()->to("dakoii/locations/llgs/{$districtId}");
    }

    /**
     * Download LLG CSV sample file
     */
    public function downloadLlgsSample($districtId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Get district details for sample
        $district = $this->districtModel->find($districtId);
        $districtName = $district ? $district['name'] : 'District';

        $filename = 'llgs_sample_' . strtolower(str_replace(' ', '_', $districtName)) . '.csv';
        $content = "llgcode,name\n";
        $content .= "140101,\"Ambunti Urban LLG\"\n";
        $content .= "140102,\"Drekikier LLG\"\n";
        $content .= "140201,\"Angoram Urban LLG\"\n";

        return $this->response
            ->setHeader('Content-Type', 'text/csv')
            ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->setBody($content);
    }

    /**
     * Show ward CSV import form (GET)
     */
    public function importWardsForm($llgId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Get LLG details
        $llg = $this->llgModel->find($llgId);
        if (!$llg) {
            session()->setFlashdata('error', 'LLG not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        // Get district and province details
        $district = $this->districtModel->find($llg['district_id']);
        $province = $this->provinceModel->find($llg['province_id']);

        $data['title'] = "Import Wards for " . $llg['name'];
        $data['menu'] = "locations";
        $data['llg'] = $llg;
        $data['district'] = $district;
        $data['province'] = $province;

        return view('dakoii/dakoii_locations_import_wards', $data);
    }

    /**
     * Process ward CSV import (POST)
     */
    public function importWards($llgId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Get LLG details
        $llg = $this->llgModel->find($llgId);
        if (!$llg) {
            session()->setFlashdata('error', 'LLG not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        // Validate file upload
        $rules = [
            'csv_file' => 'uploaded[csv_file]|max_size[csv_file,2048]|ext_in[csv_file,csv]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please upload a valid CSV file (max 2MB)');
            return redirect()->to("dakoii/locations/wards/import/{$llgId}")->withInput();
        }

        $file = $this->request->getFile('csv_file');

        if (!$file->isValid()) {
            session()->setFlashdata('error', 'Invalid file upload');
            return redirect()->to("dakoii/locations/wards/import/{$llgId}");
        }

        // Use the country from the LLG (which comes from district/province)
        $countryId = $llg['country_id'];
        $results = [
            'total' => 0,
            'imported' => 0,
            'skipped' => 0,
            'errors' => []
        ];

        // Parse CSV file
        $handle = fopen($file->getTempName(), 'r');
        $header = fgetcsv($handle); // Skip header row

        // Validate header - only need wardcode and name since we know the LLG
        if (!$header || count($header) < 2 ||
            strtolower(trim($header[0])) !== 'wardcode' ||
            strtolower(trim($header[1])) !== 'name') {
            fclose($handle);
            session()->setFlashdata('error', 'Invalid CSV format. Expected columns: wardcode, name');
            return redirect()->to("dakoii/locations/wards/import/{$llgId}");
        }

        $db = \Config\Database::connect();
        $db->transStart();

        $lineNumber = 1;
        while (($data = fgetcsv($handle)) !== FALSE) {
            $lineNumber++;
            $results['total']++;

            if (count($data) < 2) {
                $results['errors'][] = "Line {$lineNumber}: Insufficient data";
                continue;
            }

            $wardcode = trim($data[0]);
            $name = trim($data[1]);

            if (empty($wardcode) || empty($name)) {
                $results['errors'][] = "Line {$lineNumber}: Ward code and name are required";
                continue;
            }

            // Check if ward already exists
            $existing = $this->wardModel->where('wardcode', $wardcode)->first();
            if ($existing) {
                $results['skipped']++;
                $results['errors'][] = "Line {$lineNumber}: Ward code '{$wardcode}' already exists";
                continue;
            }

            // Insert ward for the specific LLG
            $wardData = [
                'wardcode' => $wardcode,
                'name' => $name,
                'country_id' => $countryId,
                'province_id' => $llg['province_id'], // Use LLG's province
                'district_id' => $llg['district_id'], // Use LLG's district
                'llg_id' => $llgId // Use the LLG ID from the URL
            ];

            if ($this->wardModel->insert($wardData)) {
                $results['imported']++;
            } else {
                $results['errors'][] = "Line {$lineNumber}: Failed to insert ward '{$name}'";
            }
        }

        fclose($handle);
        $db->transComplete();

        if ($db->transStatus() === FALSE) {
            session()->setFlashdata('error', 'Import failed due to database error');
            return redirect()->to("dakoii/locations/wards/import/{$llgId}");
        }

        // Set success message with results
        $message = "Import completed! Imported: {$results['imported']}, Skipped: {$results['skipped']}, Total: {$results['total']}";
        if (!empty($results['errors'])) {
            $message .= "\n\nErrors:\n" . implode("\n", array_slice($results['errors'], 0, 10));
            if (count($results['errors']) > 10) {
                $message .= "\n... and " . (count($results['errors']) - 10) . " more errors";
            }
        }

        session()->setFlashdata('success', $message);
        return redirect()->to("dakoii/locations/wards/{$llgId}");
    }

    /**
     * Download ward CSV sample file
     */
    public function downloadWardsSample($llgId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Get LLG details for sample
        $llg = $this->llgModel->find($llgId);
        $llgName = $llg ? $llg['name'] : 'LLG';

        $filename = 'wards_sample_' . strtolower(str_replace(' ', '_', $llgName)) . '.csv';
        $content = "wardcode,name\n";
        $content .= "14010101,\"Ward 1\"\n";
        $content .= "14010102,\"Ward 2\"\n";
        $content .= "14010103,\"Ward 3\"\n";

        return $this->response
            ->setHeader('Content-Type', 'text/csv')
            ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->setBody($content);
    }
}
