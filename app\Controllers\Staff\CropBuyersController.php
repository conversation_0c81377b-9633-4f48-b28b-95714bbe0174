<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropBuyersModel;
use App\Models\CropsModel;
use App\Models\AdxProvinceModel;

class CropBuyersController extends BaseController
{
    protected $cropBuyersModel;
    protected $cropsModel;
    protected $provinceModel;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->cropBuyersModel = new CropBuyersModel();
        $this->cropsModel = new CropsModel();
        $this->provinceModel = new AdxProvinceModel();
    }

    /**
     * Display a listing of crop buyers
     * GET /staff/crop-buyers
     */
    public function index()
    {
        try {
            $buyers = $this->cropBuyersModel
                ->select('crop_buyers.*, adx_crops.crop_name')
                ->join('adx_crops', 'adx_crops.id = crop_buyers.crop_id', 'left')
                ->where('crop_buyers.status', 'active')
                ->orderBy('crop_buyers.name', 'ASC')
                ->findAll();

            $data = [
                'title' => 'Crop Buyers Management',
                'page_header' => 'Crop Buyers Management',
                'buyers' => $buyers
            ];

            return view('staff/crop_buyers/crop_buyers_index', $data);

        } catch (\Exception $e) {
            log_message('error', '[Crop Buyers Index] ' . $e->getMessage());
            return redirect()->to('staff')->with('error', 'Error loading crop buyers.');
        }
    }

    /**
     * Show the form for creating a new crop buyer
     * GET /staff/crop-buyers/create
     */
    public function create()
    {
        try {
            $data = [
                'title' => 'Add New Crop Buyer',
                'page_header' => 'Add New Crop Buyer',
                'crops' => $this->cropsModel->orderBy('crop_name', 'ASC')->findAll(),
                'provinces' => $this->provinceModel->orderBy('name', 'ASC')->findAll()
            ];

            return view('staff/crop_buyers/crop_buyers_create', $data);

        } catch (\Exception $e) {
            log_message('error', '[Crop Buyers Create] ' . $e->getMessage());
            return redirect()->to('staff/crop-buyers')->with('error', 'Error loading create form.');
        }
    }

    /**
     * Store a newly created crop buyer in storage
     * POST /staff/crop-buyers
     */
    public function store()
    {
        try {
            $validation = \Config\Services::validation();
            
            $rules = [
                'crop_id' => 'required|numeric',
                'name' => 'required|min_length[3]|max_length[255]',
                'contact_number' => 'permit_empty|min_length[5]|max_length[20]',
                'email' => 'permit_empty|valid_email|max_length[255]',
                'operation_span' => 'required|in_list[local,national]',
                'location_id' => 'permit_empty|numeric',
                'address' => 'permit_empty|max_length[500]',
                'description' => 'permit_empty'
            ];

            if (!$this->validate($rules)) {
                return redirect()->back()->withInput()
                    ->with('errors', $validation->getErrors());
            }

            // Generate buyer code
            $buyerCode = $this->generateBuyerCode();

            $data = [
                'crop_id' => $this->request->getPost('crop_id'),
                'buyer_code' => $buyerCode,
                'name' => $this->request->getPost('name'),
                'contact_number' => $this->request->getPost('contact_number'),
                'email' => $this->request->getPost('email'),
                'operation_span' => $this->request->getPost('operation_span'),
                'location_id' => $this->request->getPost('location_id'),
                'address' => $this->request->getPost('address'),
                'description' => $this->request->getPost('description'),
                'created_by' => session()->get('emp_id') ?? 1,
                'status' => 'active'
            ];

            if ($this->cropBuyersModel->insert($data)) {
                return redirect()->to('staff/crop-buyers')
                    ->with('success', 'Crop buyer added successfully.');
            } else {
                $errors = $this->cropBuyersModel->errors();
                return redirect()->back()->withInput()
                    ->with('error', 'Failed to add crop buyer.')
                    ->with('validation_errors', $errors);
            }

        } catch (\Exception $e) {
            log_message('error', '[Crop Buyers Store] ' . $e->getMessage());
            return redirect()->back()->withInput()
                ->with('error', 'An error occurred while adding the crop buyer. Please try again.');
        }
    }

    /**
     * Display the specified crop buyer
     * GET /staff/crop-buyers/{id}
     */
    public function show($id)
    {
        try {
            $buyer = $this->cropBuyersModel
                ->select('crop_buyers.*, adx_crops.crop_name')
                ->join('adx_crops', 'adx_crops.id = crop_buyers.crop_id', 'left')
                ->find($id);

            if (!$buyer) {
                return redirect()->to('staff/crop-buyers')
                    ->with('error', 'Crop buyer not found.');
            }

            $data = [
                'title' => 'Crop Buyer Details',
                'page_header' => 'Crop Buyer Details',
                'buyer' => $buyer
            ];

            return view('staff/crop_buyers/crop_buyers_show', $data);

        } catch (\Exception $e) {
            log_message('error', '[Crop Buyers Show] ' . $e->getMessage());
            return redirect()->to('staff/crop-buyers')
                ->with('error', 'Error loading crop buyer details.');
        }
    }

    /**
     * Show the form for editing the specified crop buyer
     * GET /staff/crop-buyers/{id}/edit
     */
    public function edit($id)
    {
        try {
            $buyer = $this->cropBuyersModel->find($id);

            if (!$buyer) {
                return redirect()->to('staff/crop-buyers')
                    ->with('error', 'Crop buyer not found.');
            }

            $data = [
                'title' => 'Edit Crop Buyer',
                'page_header' => 'Edit Crop Buyer',
                'buyer' => $buyer,
                'crops' => $this->cropsModel->orderBy('crop_name', 'ASC')->findAll(),
                'provinces' => $this->provinceModel->orderBy('name', 'ASC')->findAll()
            ];

            return view('staff/crop_buyers/crop_buyers_edit', $data);

        } catch (\Exception $e) {
            log_message('error', '[Crop Buyers Edit] ' . $e->getMessage());
            return redirect()->to('staff/crop-buyers')
                ->with('error', 'Error loading edit form.');
        }
    }

    /**
     * Update the specified crop buyer in storage
     * PUT /staff/crop-buyers/{id}
     * POST /staff/crop-buyers/{id} (form compatibility)
     */
    public function update($id)
    {
        try {
            $buyer = $this->cropBuyersModel->find($id);

            if (!$buyer) {
                return redirect()->to('staff/crop-buyers')
                    ->with('error', 'Crop buyer not found.');
            }

            $validation = \Config\Services::validation();
            
            $rules = [
                'crop_id' => 'required|numeric',
                'name' => 'required|min_length[3]|max_length[255]',
                'contact_number' => 'permit_empty|min_length[5]|max_length[20]',
                'email' => 'permit_empty|valid_email|max_length[255]',
                'operation_span' => 'required|in_list[local,national]',
                'location_id' => 'permit_empty|numeric',
                'address' => 'permit_empty|max_length[500]',
                'description' => 'permit_empty'
            ];

            if (!$this->validate($rules)) {
                return redirect()->back()->withInput()
                    ->with('errors', $validation->getErrors());
            }

            $data = [
                'crop_id' => $this->request->getPost('crop_id'),
                'name' => $this->request->getPost('name'),
                'contact_number' => $this->request->getPost('contact_number'),
                'email' => $this->request->getPost('email'),
                'operation_span' => $this->request->getPost('operation_span'),
                'location_id' => $this->request->getPost('location_id'),
                'address' => $this->request->getPost('address'),
                'description' => $this->request->getPost('description'),
                'updated_by' => session()->get('emp_id') ?? 1
            ];

            if ($this->cropBuyersModel->update($id, $data)) {
                return redirect()->to('staff/crop-buyers')
                    ->with('success', 'Crop buyer updated successfully.');
            } else {
                $errors = $this->cropBuyersModel->errors();
                return redirect()->back()->withInput()
                    ->with('error', 'Failed to update crop buyer.')
                    ->with('validation_errors', $errors);
            }

        } catch (\Exception $e) {
            log_message('error', '[Crop Buyers Update] ' . $e->getMessage());
            return redirect()->back()->withInput()
                ->with('error', 'An error occurred while updating the crop buyer. Please try again.');
        }
    }



    /**
     * Generate a unique buyer code
     */
    private function generateBuyerCode()
    {
        $prefix = 'CB';
        $year = date('Y');
        
        // Get the last buyer code for this year
        $lastBuyer = $this->cropBuyersModel
            ->where('buyer_code LIKE', $prefix . $year . '%')
            ->orderBy('buyer_code', 'DESC')
            ->first();

        if ($lastBuyer) {
            $lastNumber = (int) substr($lastBuyer['buyer_code'], -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
