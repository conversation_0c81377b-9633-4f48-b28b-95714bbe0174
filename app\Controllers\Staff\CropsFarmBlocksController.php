<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\{
    CropsFarmBlockModel,
    FarmerInformationModel,
    CropsModel,
    AdxDistrictModel,
    AdxLlgModel,
    AdxWardModel
};

class CropsFarmBlocksController extends BaseController
{
    protected $farmBlockModel;
    protected $farmerModel;
    protected $cropsModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;

    public function __construct()
    {
        $this->farmBlockModel = new CropsFarmBlockModel();
        $this->farmerModel = new FarmerInformationModel();
        $this->cropsModel = new CropsModel();
        $this->districtModel = new AdxDistrictModel();
        $this->llgModel = new AdxLlgModel();
        $this->wardModel = new AdxWardModel();
        
        // Initialize helpers
        helper(['form', 'url', 'info']);
    }

    /**
     * Display a listing of crops farm blocks
     */
    public function index()
    {
        // Debug: Check if we reach this point
        log_message('info', 'CropsFarmBlocksController::index() called');

        $district_id = session()->get('district_id');
        $farm_blocks = $this->farmBlockModel->getFarmBlocksWithDetails($district_id);

        // Debug: Log the data
        log_message('info', 'District ID: ' . $district_id);
        log_message('info', 'Farm blocks count: ' . count($farm_blocks));

        $data = [
            'title' => 'Crops Farm Blocks',
            'page_header' => 'Crops Farm Blocks Management',
            'farm_blocks' => $farm_blocks
        ];

        return view('staff/crops_farm_blocks/crops_farm_blocks_index', $data);
    }

    /**
     * Show the form for creating a new crops farm block
     */
    public function create()
    {
        $district_id = session()->get('district_id');

        // Get all wards for the district (no dependency)
        $wards = $this->wardModel->where('district_id', $district_id)->findAll();

        $data = [
            'title' => 'Create Crops Farm Block',
            'page_header' => 'Create New Crops Farm Block',
            'farmers' => $this->farmerModel->where('district_id', $district_id)
                                          ->where('status', 'active')
                                          ->findAll(),
            'crops' => $this->cropsModel->findAll(),
            'llgs' => $this->llgModel->where('district_id', $district_id)->findAll(),
            'wards' => $wards
        ];

        return view('staff/crops_farm_blocks/crops_farm_blocks_create', $data);
    }

    /**
     * Store a newly created crops farm block
     */
    public function store()
    {
        $validation = \Config\Services::validation();
        
        $rules = [
            'farmer_id' => 'required|numeric',
            'crop_id' => 'required|numeric',
            'llg_id' => 'required|numeric',
            'ward_id' => 'required|numeric',
            'village' => 'required|max_length[100]',
            'block_site' => 'required|max_length[200]',
            'lon' => 'permit_empty|max_length[50]',
            'lat' => 'permit_empty|max_length[50]',
            'remarks' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        // Get location data from selected LLG
        $llgId = $this->request->getPost('llg_id');
        $llg = $this->llgModel->select('country_id, province_id, district_id')->find($llgId);

        if (!$llg) {
            return redirect()->back()->withInput()->with('error', 'Invalid LLG selected.');
        }

        // Auto-generate block code using province from LLG
        $blockCode = $this->farmBlockModel->generateBlockCode($llg['province_id']);

        $data = [
            'exercise_id' => session()->get('exercise_id'), // nullable
            'farmer_id' => $this->request->getPost('farmer_id'),
            'crop_id' => $this->request->getPost('crop_id'),
            'block_code' => $blockCode,
            'org_id' => session()->get('org_id'),
            'country_id' => $llg['country_id'],
            'province_id' => $llg['province_id'],
            'district_id' => $llg['district_id'],
            'llg_id' => $this->request->getPost('llg_id'),
            'ward_id' => $this->request->getPost('ward_id'),
            'village' => $this->request->getPost('village'),
            'block_site' => $this->request->getPost('block_site'),
            'lon' => $this->request->getPost('lon'),
            'lat' => $this->request->getPost('lat'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => 'active',
            'created_by' => session()->get('emp_id')
        ];

        // Debug log the data being saved
        log_message('info', '[Create Crops Farm Block] Data to save: ' . json_encode($data));

        try {
            $result = $this->farmBlockModel->save($data);

            if ($result === false) {
                // Get validation errors from model
                $errors = $this->farmBlockModel->errors();
                log_message('error', '[Create Crops Farm Block] Validation failed: ' . json_encode($errors));
                return redirect()->back()->withInput()->with('errors', $errors);
            }

            return redirect()->to('/staff/crops-farm-blocks')->with('success', 'Crops farm block created successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Create Crops Farm Block] Exception: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Failed to create crops farm block: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified crops farm block
     */
    public function show($id)
    {
        $district_id = session()->get('district_id');
        
        $farmBlock = $this->farmBlockModel->getFarmBlockWithDetails($id, $district_id);
        
        if (!$farmBlock) {
            return redirect()->to('/staff/crops-farm-blocks')->with('error', 'Crops farm block not found.');
        }

        $data = [
            'title' => 'View Crops Farm Block',
            'page_header' => 'Crops Farm Block Details',
            'farm_block' => $farmBlock
        ];

        return view('staff/crops_farm_blocks/crops_farm_blocks_show', $data);
    }

    /**
     * Show the form for editing the specified crops farm block
     */
    public function edit($id)
    {
        $district_id = session()->get('district_id');
        
        $farmBlock = $this->farmBlockModel->where('id', $id)
                                         ->where('district_id', $district_id)
                                         ->where('status !=', 'deleted')
                                         ->first();
        
        if (!$farmBlock) {
            return redirect()->to('/staff/crops-farm-blocks')->with('error', 'Crops farm block not found.');
        }

        $data = [
            'title' => 'Edit Crops Farm Block',
            'page_header' => 'Edit Crops Farm Block',
            'farm_block' => $farmBlock,
            'farmers' => $this->farmerModel->where('district_id', $district_id)
                                          ->where('status', 'active')
                                          ->findAll(),
            'crops' => $this->cropsModel->findAll(),
            'llgs' => $this->llgModel->where('district_id', $district_id)->findAll(),
            'wards' => $this->wardModel->where('district_id', $district_id)->findAll()
        ];

        return view('staff/crops_farm_blocks/crops_farm_blocks_edit', $data);
    }

    /**
     * Update the specified crops farm block
     */
    public function update($id)
    {
        $district_id = session()->get('district_id');
        
        $farmBlock = $this->farmBlockModel->where('id', $id)
                                         ->where('district_id', $district_id)
                                         ->where('status !=', 'deleted')
                                         ->first();
        
        if (!$farmBlock) {
            return redirect()->to('/staff/crops-farm-blocks')->with('error', 'Crops farm block not found.');
        }

        $validation = \Config\Services::validation();
        
        $rules = [
            'farmer_id' => 'required|numeric',
            'crop_id' => 'required|numeric',
            'llg_id' => 'required|numeric',
            'ward_id' => 'required|numeric',
            'village' => 'required|max_length[100]',
            'block_site' => 'required|max_length[200]',
            'lon' => 'permit_empty|max_length[50]',
            'lat' => 'permit_empty|max_length[50]',
            'remarks' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'farmer_id' => $this->request->getPost('farmer_id'),
            'crop_id' => $this->request->getPost('crop_id'),
            'llg_id' => $this->request->getPost('llg_id'),
            'ward_id' => $this->request->getPost('ward_id'),
            'village' => $this->request->getPost('village'),
            'block_site' => $this->request->getPost('block_site'),
            'lon' => $this->request->getPost('lon'),
            'lat' => $this->request->getPost('lat'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('emp_id')
        ];

        try {
            $this->farmBlockModel->update($id, $data);
            return redirect()->to('/staff/crops-farm-blocks')->with('success', 'Crops farm block updated successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Update Crops Farm Block] ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Failed to update crops farm block. Please try again.');
        }
    }

    /**
     * Remove the specified crops farm block (soft delete)
     */
    public function destroy($id)
    {
        $district_id = session()->get('district_id');
        
        $farmBlock = $this->farmBlockModel->where('id', $id)
                                         ->where('district_id', $district_id)
                                         ->where('status !=', 'deleted')
                                         ->first();
        
        if (!$farmBlock) {
            return redirect()->to('/staff/crops-farm-blocks')->with('error', 'Crops farm block not found.');
        }

        try {
            $this->farmBlockModel->softDelete($id, session()->get('emp_id'));
            return redirect()->to('/staff/crops-farm-blocks')->with('success', 'Crops farm block deleted successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Delete Crops Farm Block] ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to delete crops farm block. Please try again.');
        }
    }


}
