<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropsFarmCropsDataModel;
use App\Models\CropsFarmBlockModel;
use App\Models\CropsModel;
use App\Models\FarmerInformationModel;
use App\Models\usersModel;
use App\Models\districtModel;
use App\Models\provinceModel;
use App\Models\llgModel;
use App\Models\wardModel;

class StaffCropsData extends BaseController
{
    private $models = [];
    protected $helpers = ['url', 'form', 'info', 'weather'];

    public function __construct()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'user') {
            throw new \Exception('Unauthorized access');
        }

        foreach ($this->helpers as $helper) {
            helper($helper);
        }
    }

    protected function getModel($modelName)
    {
        if (!isset($this->models[$modelName])) {
            switch ($modelName) {
                case 'farmCropsData':
                    $this->models[$modelName] = new CropsFarmCropsDataModel();
                    break;
                case 'farmBlocks':
                    $this->models[$modelName] = new CropsFarmBlockModel();
                    break;
                case 'crops':
                    $this->models[$modelName] = new CropsModel();
                    break;
                case 'farmers':
                    $this->models[$modelName] = new FarmerInformationModel();
                    break;
                case 'users':
                    $this->models[$modelName] = new usersModel();
                    break;
                case 'districts':
                    $this->models[$modelName] = new districtModel();
                    break;
                case 'provinces':
                    $this->models[$modelName] = new provinceModel();
                    break;
                case 'llgs':
                    $this->models[$modelName] = new llgModel();
                    break;
                case 'wards':
                    $this->models[$modelName] = new wardModel();
                    break;
            }
        }
        return $this->models[$modelName];
    }

    // Helper methods to get specific models
    protected function getFarmCropsDataModel() { return $this->getModel('farmCropsData'); }
    protected function getFarmBlockModel() { return $this->getModel('farmBlocks'); }
    protected function getCropsModel() { return $this->getModel('crops'); }
    protected function getFarmersModel() { return $this->getModel('farmers'); }
    protected function getUsersModel() { return $this->getModel('users'); }
    protected function getDistrictModel() { return $this->getModel('districts'); }
    protected function getProvinceModel() { return $this->getModel('provinces'); }
    protected function getLlgModel() { return $this->getModel('llgs'); }
    protected function getWardModel() { return $this->getModel('wards'); }

    protected function verifyDistrictAccess($districtId)
    {
        return $districtId == session()->get('district_id');
    }

    protected function validateInput($data, $required = [])
    {
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \Exception("The {$field} field is required.");
            }
        }

        array_walk_recursive($data, function(&$value) {
            $value = strip_tags($value);
            $value = trim($value);
        });

        return $data;
    }

    public function view_crop_blocks()
    {
        $district = $this->getModel('districts')->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Farm Blocks List',
            'page_header' => 'Farm Blocks List',
            'farmers' => $this->getFarmersModel()->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'farm_blocks' => $this->getFarmBlockModel()->select('
                crops_farm_blocks.*,
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'crops' => $this->getCropsModel()->findAll(),
        ];

        return view('staff/farms/view_crop_blocks', $data);
    }

    public function view_crops_data($block_id)
    {
        $block = $this->getFarmBlockModel()->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();

        if (!$block || ($block['crop_id'] == 0)) {
            return redirect()->back()->with('error', 'Block not found or Crop not selected');
        }

        $data = [
            'title' => 'Crops Data',
            'page_header' => 'Crops Data',
            'block' => $block,
            'crops_data' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('status', 'active')
                ->orderBy('action_date', 'DESC')
                ->findAll(),
            'total_hectares_added' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('action_type', 'add')
                ->selectSum('hectares', 'total_hectares_added')
                ->first(),
            'total_hectares_removed' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('action_type', 'remove')
                ->selectSum('hectares', 'total_hectares_removed')
                ->first(),
            'total_plants_added' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('action_type', 'add')
                ->selectSum('number_of_plants', 'total_plants_added')
                ->first(),
            'total_plants_removed' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('action_type', 'remove')
                ->selectSum('number_of_plants', 'total_plants_removed')
                ->first(),
            'farmer' => $this->getFarmersModel()->find($block['farmer_id']),
            'province' => $this->getModel('provinces')->find($block['province_id']),
            'district' => $this->getModel('districts')->find($block['district_id']),
            'llg' => $this->getModel('llgs')->find($block['llg_id']),
            'ward' => $this->getModel('wards')->find($block['ward_id']),
            'crop' => $this->getCropsModel()->find($block['crop_id']),
            'users' => $this->getUsersModel()->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff/farms/view_crops_data', $data);
    }

    public function add_crops_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');

            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $block_id)
                ->where('district_id', session()->get('district_id'))
                ->first();

            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'block_id' => $block_id,
                'action_type' => $this->request->getPost('action_type'),
                'action_reason' => $this->request->getPost('action_reason'),
                'action_date' => $this->request->getPost('action_date'),
                'number_of_plants' => $this->request->getPost('number_of_plants'),
                'breed' => $this->request->getPost('breed'),
                'hectares' => $this->request->getPost('hectares'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['action_type', 'action_date', 'number_of_plants', 'hectares'];
            $this->validateInput($data, $required);

            $this->getFarmCropsDataModel()->save($data);

            //get weather data
            $weather_data = get_weather_data($block['id'], $block['lon'], $block['lat'], $data['action_date']);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Crops data added successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Crops Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_crops_data()
    {
        try {
            $id = $this->request->getPost('id');
            $crops_data = $this->getFarmCropsDataModel()->find($id);

            if (!$crops_data) {
                throw new \Exception('Record not found');
            }

            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $crops_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();

            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'action_type' => $this->request->getPost('action_type'),
                'action_reason' => $this->request->getPost('action_reason'),
                'action_date' => $this->request->getPost('action_date'),
                'number_of_plants' => $this->request->getPost('number_of_plants'),
                'breed' => $this->request->getPost('breed'),
                'hectares' => $this->request->getPost('hectares'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate required fields
            $required = ['action_type', 'action_date', 'number_of_plants', 'hectares'];
            $this->validateInput($data, $required);

            $this->getFarmCropsDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Crops data updated successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Crops Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete_crops_data($id)
    {
        try {
            $crops_data = $this->getFarmCropsDataModel()->find($id);

            if (!$crops_data) {
                throw new \Exception('Record not found');
            }

            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $crops_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();

            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmCropsDataModel()->update($id, $data);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Record deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Crops Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    // Simple CRUD methods for crops data
    public function crops_data_index()
    {
        $district = $this->getModel('districts')->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Crops Data Management',
            'page_header' => 'Crops Data Management',
            'page_desc' => 'Select a farm block to manage crops data',
            'menu' => 'crops-data',
            'farmers' => $this->getFarmersModel()->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'farm_blocks' => $this->getFarmBlockModel()->select('
                crops_farm_blocks.*,
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'crops' => $this->getCropsModel()->findAll(),
        ];

        return view('staff_crops_data/staff_crops_data_index', $data);
    }

    public function crops_data_show($block_id)
    {
        $block = $this->getFarmBlockModel()->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();

        $data = [
            'title' => 'Crops Data - ' . $block['block_code'],
            'page_header' => 'Crops Data Management',
            'page_desc' => 'Manage crops data for farm block: ' . $block['block_code'],
            'menu' => 'crops-data',
            'block' => $block,
            'crops_data' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('status', 'active')
                ->orderBy('action_date', 'DESC')
                ->findAll(),
            'total_hectares_added' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('action_type', 'add')
                ->selectSum('hectares', 'total_hectares_added')
                ->first(),
            'total_hectares_removed' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('action_type', 'remove')
                ->selectSum('hectares', 'total_hectares_removed')
                ->first(),
            'total_plants_added' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('action_type', 'add')
                ->selectSum('number_of_plants', 'total_plants_added')
                ->first(),
            'total_plants_removed' => $this->getFarmCropsDataModel()->where('block_id', $block_id)
                ->where('action_type', 'remove')
                ->selectSum('number_of_plants', 'total_plants_removed')
                ->first(),
            'farmer' => $this->getFarmersModel()->find($block['farmer_id']),
            'province' => $this->getModel('provinces')->find($block['province_id']),
            'district' => $this->getModel('districts')->find($block['district_id']),
            'llg' => $this->getModel('llgs')->find($block['llg_id']),
            'ward' => $this->getModel('wards')->find($block['ward_id']),
            'crop' => $this->getCropsModel()->find($block['crop_id']),
            'users' => $this->getUsersModel()->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff_crops_data/staff_crops_data_show', $data);
    }

    public function crops_data_create($block_id)
    {
        $block = $this->getFarmBlockModel()->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();

        $data = [
            'title' => 'Add Crops Data',
            'page_header' => 'Add Crops Data',
            'page_desc' => 'Add new crops data for farm block: ' . $block['block_code'],
            'menu' => 'crops-data',
            'block' => $block,
            'farmer' => $this->getFarmersModel()->find($block['farmer_id']),
            'crop' => $this->getCropsModel()->find($block['crop_id']),
        ];

        return view('staff_crops_data/staff_crops_data_create', $data);
    }

    public function crops_data_store()
    {
        $block_id = $this->request->getPost('block_id');
        $block = $this->getFarmBlockModel()->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();

        $data = [
            'exercise_id' => session()->get('exercise_id'),
            'block_id' => $block_id,
            'crop_id' => $block['crop_id'],
            'action_type' => $this->request->getPost('action_type'),
            'action_reason' => $this->request->getPost('action_reason'),
            'action_date' => $this->request->getPost('action_date'),
            'number_of_plants' => $this->request->getPost('number_of_plants'),
            'breed' => $this->request->getPost('breed'),
            'hectares' => $this->request->getPost('hectares'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => session()->get('emp_id'),
            'status' => 'active'
        ];

        $this->getFarmCropsDataModel()->save($data);
        return $this->crops_data_show($block_id);
    }

    public function crops_data_edit($id)
    {
        $crops_data = $this->getFarmCropsDataModel()->find($id);
        $block = $this->getFarmBlockModel()->where('id', $crops_data['block_id'])
            ->where('district_id', session()->get('district_id'))
            ->first();

        $data = [
            'title' => 'Edit Crops Data',
            'page_header' => 'Edit Crops Data',
            'page_desc' => 'Edit crops data for farm block: ' . $block['block_code'],
            'menu' => 'crops-data',
            'crops_data' => $crops_data,
            'block' => $block,
            'farmer' => $this->getFarmersModel()->find($block['farmer_id']),
            'crop' => $this->getCropsModel()->find($block['crop_id']),
        ];

        return view('staff_crops_data/staff_crops_data_edit', $data);
    }

    public function crops_data_update($id)
    {
        $crops_data = $this->getFarmCropsDataModel()->find($id);
        $block = $this->getFarmBlockModel()->where('id', $crops_data['block_id'])
            ->where('district_id', session()->get('district_id'))
            ->first();

        $data = [
            'action_type' => $this->request->getPost('action_type'),
            'action_reason' => $this->request->getPost('action_reason'),
            'action_date' => $this->request->getPost('action_date'),
            'number_of_plants' => $this->request->getPost('number_of_plants'),
            'breed' => $this->request->getPost('breed'),
            'hectares' => $this->request->getPost('hectares'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('emp_id'),
        ];

        $this->getFarmCropsDataModel()->update($id, $data);
        return $this->crops_data_show($crops_data['block_id']);
    }

    public function crops_data_delete($id)
    {
        $crops_data = $this->getFarmCropsDataModel()->find($id);
        $block = $this->getFarmBlockModel()->where('id', $crops_data['block_id'])
            ->where('district_id', session()->get('district_id'))
            ->first();

        $data = [
            'status' => 'deleted',
            'deleted_by' => session()->get('emp_id'),
            'deleted_at' => date('Y-m-d H:i:s')
        ];

        $this->getFarmCropsDataModel()->update($id, $data);
        return $this->crops_data_show($crops_data['block_id']);
    }
}
