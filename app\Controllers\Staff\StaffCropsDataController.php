<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropsFarmCropsDataModel;
use App\Models\CropsFarmBlockModel;
use App\Models\CropsModel;
use App\Models\FarmerInformationModel;
use App\Models\usersModel;
use App\Models\districtModel;
use App\Models\provinceModel;
use App\Models\llgModel;
use App\Models\wardModel;

class StaffCropsDataController extends BaseController
{
    protected $cropsDataModel;
    protected $farmBlockModel;
    protected $cropsModel;
    protected $farmerModel;
    protected $usersModel;
    protected $districtModel;
    protected $provinceModel;
    protected $llgModel;
    protected $wardModel;
    public function __construct()
    {
        $this->cropsDataModel = new CropsFarmCropsDataModel();
        $this->farmBlockModel = new CropsFarmBlockModel();
        $this->cropsModel = new CropsModel();
        $this->farmerModel = new FarmerInformationModel();
        $this->usersModel = new usersModel();
        $this->districtModel = new districtModel();
        $this->provinceModel = new provinceModel();
        $this->llgModel = new llgModel();
        $this->wardModel = new wardModel();
        helper(['form', 'url', 'array', 'date', 'info']);
    }

    /**
     * Display list of farm blocks (entry point for crops data)
     */
    public function index()
    {
        $district = $this->districtModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Crops Data Management',
            'page_header' => 'Crops Data Management',
            'page_desc' => 'Select a farm block to manage crops data',
            'menu' => 'crops-data',
            'farmers' => $this->farmerModel->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'farm_blocks' => $this->farmBlockModel->select('
                crops_farm_blocks.*,
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'crops' => $this->cropsModel->findAll(),
        ];

        return view('staff_crops_data/staff_crops_data_index', $data);
    }

    /**
     * Display crops data for a specific farm block
     */
    public function show($block_id)
    {
        $block = $this->farmBlockModel->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();

        if (!$block || ($block['crop_id'] == 0)) {
            return redirect()->to('staff/crops-data')->with('error', 'Block not found or Crop not selected');
        }

        $data = [
            'title' => 'Crops Data - ' . $block['block_code'],
            'page_header' => 'Crops Data Management',
            'page_desc' => 'Manage crops data for farm block: ' . $block['block_code'],
            'menu' => 'crops-data',
            'block' => $block,
            'crops_data' => $this->cropsDataModel->where('block_id', $block_id)
                ->where('status', 'active')
                ->orderBy('action_date', 'DESC')
                ->findAll(),
            'total_hectares_added' => $this->cropsDataModel->where('block_id', $block_id)
                ->where('action_type', 'add')
                ->selectSum('hectares', 'total_hectares_added')
                ->first(),
            'total_hectares_removed' => $this->cropsDataModel->where('block_id', $block_id)
                ->where('action_type', 'remove')
                ->selectSum('hectares', 'total_hectares_removed')
                ->first(),
            'total_plants_added' => $this->cropsDataModel->where('block_id', $block_id)
                ->where('action_type', 'add')
                ->selectSum('number_of_plants', 'total_plants_added')
                ->first(),
            'total_plants_removed' => $this->cropsDataModel->where('block_id', $block_id)
                ->where('action_type', 'remove')
                ->selectSum('number_of_plants', 'total_plants_removed')
                ->first(),
            'farmer' => $this->farmerModel->find($block['farmer_id']),
            'province' => $this->provinceModel->find($block['province_id']),
            'district' => $this->districtModel->find($block['district_id']),
            'llg' => $this->llgModel->find($block['llg_id']),
            'ward' => $this->wardModel->find($block['ward_id']),
            'crop' => $this->cropsModel->find($block['crop_id']),
            'users' => $this->usersModel->where('org_id', session()->get('org_id'))->findAll(),
        ];

        return view('staff_crops_data/staff_crops_data_show', $data);
    }

    /**
     * Show create form for crops data
     */
    public function create($block_id)
    {
        $block = $this->farmBlockModel->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();

        if (!$block || ($block['crop_id'] == 0)) {
            return redirect()->to('staff/crops-data')->with('error', 'Block not found or Crop not selected');
        }

        $data = [
            'title' => 'Add Crops Data',
            'page_header' => 'Add Crops Data',
            'page_desc' => 'Add new crops data for farm block: ' . $block['block_code'],
            'menu' => 'crops-data',
            'block' => $block,
            'farmer' => $this->farmerModel->find($block['farmer_id']),
            'crop' => $this->cropsModel->find($block['crop_id']),
        ];

        return view('staff_crops_data/staff_crops_data_create', $data);
    }

    /**
     * Store new crops data
     */
    public function store()
    {
        $block_id = $this->request->getPost('block_id');

        // Verify block belongs to user's district
        $block = $this->farmBlockModel->where('id', $block_id)
            ->where('district_id', session()->get('district_id'))
            ->first();

        if (!$block) {
            return redirect()->to('staff/crops-data')->with('error', 'Access denied');
        }

        $data = [
            'exercise_id' => session()->get('exercise_id'),
            'block_id' => $block_id,
            'crop_id' => $block['crop_id'],
            'action_type' => $this->request->getPost('action_type'),
            'action_reason' => $this->request->getPost('action_reason'),
            'action_date' => $this->request->getPost('action_date'),
            'number_of_plants' => $this->request->getPost('number_of_plants'),
            'breed' => $this->request->getPost('breed'),
            'hectares' => $this->request->getPost('hectares'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => session()->get('emp_id'),
            'status' => 'active'
        ];

        if ($this->cropsDataModel->save($data)) {
            // Get weather data
            $weather_data = get_weather_data($block['id'], $block['lon'], $block['lat'], $data['action_date']);
            
            return redirect()->to('staff/crops-data/' . $block_id)->with('success', 'Crops data added successfully!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to save crops data. Please try again.');
        }
    }

    /**
     * Show edit form for crops data
     */
    public function edit($id)
    {
        $crops_data = $this->cropsDataModel->find($id);

        if (!$crops_data) {
            return redirect()->to('staff/crops-data')->with('error', 'Record not found');
        }

        // Verify block belongs to user's district
        $block = $this->farmBlockModel->where('id', $crops_data['block_id'])
            ->where('district_id', session()->get('district_id'))
            ->first();

        if (!$block) {
            return redirect()->to('staff/crops-data')->with('error', 'Access denied');
        }

        $data = [
            'title' => 'Edit Crops Data',
            'page_header' => 'Edit Crops Data',
            'page_desc' => 'Edit crops data for farm block: ' . $block['block_code'],
            'menu' => 'crops-data',
            'crops_data' => $crops_data,
            'block' => $block,
            'farmer' => $this->farmerModel->find($block['farmer_id']),
            'crop' => $this->cropsModel->find($block['crop_id']),
        ];

        return view('staff_crops_data/staff_crops_data_edit', $data);
    }

    /**
     * Update crops data
     */
    public function update($id)
    {
        $crops_data = $this->cropsDataModel->find($id);

        if (!$crops_data) {
            return redirect()->to('staff/crops-data')->with('error', 'Record not found');
        }

        // Verify block belongs to user's district
        $block = $this->farmBlockModel->where('id', $crops_data['block_id'])
            ->where('district_id', session()->get('district_id'))
            ->first();

        if (!$block) {
            return redirect()->to('staff/crops-data')->with('error', 'Access denied');
        }

        $data = [
            'action_type' => $this->request->getPost('action_type'),
            'action_reason' => $this->request->getPost('action_reason'),
            'action_date' => $this->request->getPost('action_date'),
            'number_of_plants' => $this->request->getPost('number_of_plants'),
            'breed' => $this->request->getPost('breed'),
            'hectares' => $this->request->getPost('hectares'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('emp_id'),
        ];

        if ($this->cropsDataModel->update($id, $data)) {
            return redirect()->to('staff/crops-data/' . $crops_data['block_id'])->with('success', 'Crops data updated successfully!');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update crops data. Please try again.');
        }
    }

    /**
     * Delete crops data
     */
    public function destroy($id)
    {
        $crops_data = $this->cropsDataModel->find($id);

        if (!$crops_data) {
            return redirect()->to('staff/crops-data')->with('error', 'Record not found');
        }

        // Verify block belongs to user's district
        $block = $this->farmBlockModel->where('id', $crops_data['block_id'])
            ->where('district_id', session()->get('district_id'))
            ->first();

        if (!$block) {
            return redirect()->to('staff/crops-data')->with('error', 'Access denied');
        }

        $data = [
            'status' => 'deleted',
            'deleted_by' => session()->get('emp_id'),
            'deleted_at' => date('Y-m-d H:i:s')
        ];

        if ($this->cropsDataModel->update($id, $data)) {
            return redirect()->to('staff/crops-data/' . $crops_data['block_id'])->with('success', 'Record deleted successfully');
        } else {
            return redirect()->back()->with('error', 'Failed to delete record. Please try again.');
        }
    }
}
