<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\CropsFarmDiseaseDataModel;
use App\Models\InfectionsModel;
use App\Models\CropsFarmBlockModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;
use App\Models\usersModel;

class StaffDisease extends BaseController
{
    private $models = [];
    protected $helpers = ['url', 'form', 'info'];

    public function __construct()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'user') {
            throw new \Exception('Unauthorized access');
        }

        foreach ($this->helpers as $helper) {
            helper($helper);
        }
    }

    protected function getModel($modelName)
    {
        if (!isset($this->models[$modelName])) {
            switch ($modelName) {
                case 'farmDiseaseData':
                    $this->models[$modelName] = new CropsFarmDiseaseDataModel();
                    break;
                case 'infections':
                    $this->models[$modelName] = new InfectionsModel();
                    break;
                case 'farmBlocks':
                    $this->models[$modelName] = new CropsFarmBlockModel();
                    break;
                case 'farmers':
                    $this->models[$modelName] = new FarmerInformationModel();
                    break;
                case 'crops':
                    $this->models[$modelName] = new CropsModel();
                    break;
                case 'districts':
                    $this->models[$modelName] = new AdxDistrictModel();
                    break;
                case 'provinces':
                    $this->models[$modelName] = new AdxProvinceModel();
                    break;
                case 'llgs':
                    $this->models[$modelName] = new AdxLlgModel();
                    break;
                case 'wards':
                    $this->models[$modelName] = new AdxWardModel();
                    break;
                case 'users':
                    $this->models[$modelName] = new usersModel();
                    break;
            }
        }
        return $this->models[$modelName];
    }

    // Helper methods to get specific models
    protected function getFarmDiseaseDataModel() { return $this->getModel('farmDiseaseData'); }
    protected function getInfectionsModel() { return $this->getModel('infections'); }
    protected function getFarmBlockModel() { return $this->getModel('farmBlocks'); }
    protected function getFarmersModel() { return $this->getModel('farmers'); }
    protected function getCropsModel() { return $this->getModel('crops'); }
    protected function getDistrictModel() { return $this->getModel('districts'); }
    protected function getProvinceModel() { return $this->getModel('provinces'); }
    protected function getLlgModel() { return $this->getModel('llgs'); }
    protected function getWardModel() { return $this->getModel('wards'); }
    protected function getUsersModel() { return $this->getModel('users'); }

    protected function verifyDistrictAccess($districtId) 
    {
        return $districtId == session()->get('district_id');
    }

    protected function validateInput($data, $required = [])
    {
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \Exception("The {$field} field is required.");
            }
        }

        array_walk_recursive($data, function(&$value) {
            $value = strip_tags($value);
            $value = trim($value);
        });

        return $data;
    }

    /**
     * Display disease data overview with optimized queries and proper joins
     * @return string
     */
    public function disease_data()
    {
        try {
            // Check if user is logged in and has district access
            if (!session()->get('district_id')) {
                log_message('error', '[Disease Data] No district assigned to user');
                return redirect()->back()->with('error', 'No district assigned to your account');
            }

            $district = $this->getModel('districts')->find(session()->get('district_id'));
            if (!$district) {
                log_message('error', '[Disease Data] District not found: ' . session()->get('district_id'));
                return redirect()->back()->with('error', 'District not found');
            }

            $districtName = $district['name'] ?? 'No District Assigned';

            // Get farm blocks with proper error handling
            $farmBlocks = $this->getFarmBlockModel()->select('
                crops_farm_blocks.*, 
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name,
                adx_district.name as district_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where([
                'crops_farm_blocks.district_id' => session()->get('district_id'),
                'crops_farm_blocks.status' => 'active'
            ])
            ->findAll();

            if ($farmBlocks === null) {
                log_message('error', '[Disease Data] Error fetching farm blocks');
                throw new \Exception('Error fetching farm blocks data');
            }

            // Get infections list with error handling
            $infections = $this->getInfectionsModel()->findAll();
            if ($infections === null) {
                log_message('error', '[Disease Data] Error fetching infections list');
                throw new \Exception('Error fetching infections data');
            }

            $data = [
                'title' => 'Disease Data',
                'page_header' => 'Disease Data',
                'farm_blocks' => $farmBlocks,
                'district_name' => $districtName,
                'infections' => $infections
            ];

            return view('staff_farms/disease_data', $data);
        } catch (\Exception $e) {
            log_message('error', '[Disease Data] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * View disease data for a specific block with proper error handling
     * @param int $block_id
     * @return string
     */
    public function view_disease_data($block_id)
    {
        try {
            // Fetch block with district access verification
            $block = $this->getFarmBlockModel()->where([
                'id' => $block_id,
                'district_id' => session()->get('district_id')
            ])->first();
                
            if (!$block) {
                return redirect()->back()->with('error', 'Block not found or access denied');
            }

            // Fetch disease data with proper error handling
            $diseases_data = $this->getFarmDiseaseDataModel()
                ->where('block_id', $block_id)
                ->where('status', 'active')
                ->orderBy('action_date', 'DESC')
                ->findAll();

            if ($diseases_data === null) {
                log_message('error', '[View Disease Data] Database error fetching disease data');
                throw new \Exception('Error retrieving disease data');
            }

            // Prepare view data
            $data = [
                'title' => 'Block Diseases Data',
                'page_header' => 'Block Diseases Data',
                'block' => $block,
                'diseases_data' => $diseases_data,
                'farmer' => $this->getModel('farmers')->find($block['farmer_id']),
                'province' => $this->getModel('provinces')->find($block['province_id']),
                'district' => $this->getModel('districts')->find($block['district_id']),
                'llg' => $this->getModel('llgs')->find($block['llg_id']),
                'ward' => $this->getModel('wards')->find($block['ward_id']),
                'crop' => $this->getModel('crops')->find($block['crop_id']),
                'infections' => $this->getInfectionsModel()->findAll(),
                'users' => $this->getUsersModel()
                    ->where('org_id', session()->get('org_id'))
                    ->findAll()
            ];

            return view('staff_farms/view_disease_data', $data);
        } catch (\Exception $e) {
            log_message('error', '[View Disease Data] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Add disease data with proper validation and error handling
     * @return \CodeIgniter\HTTP\Response
     */
    public function add_disease_data()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $block_id)
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }
            
            // Prepare disease data
            $data = $this->prepareDiseaseData($block_id);

            // Validate disease data
            $this->validateDiseaseData($data);

            // Save the data
            $this->getFarmDiseaseDataModel()->save($data);

            // Update block disease status
            $this->updateBlockDiseaseStatus($block_id);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Disease data added successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Disease Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update disease data with proper validation and error handling
     * @return \CodeIgniter\HTTP\Response
     */
    public function update_disease_data()
    {
        try {
            $id = $this->request->getPost('id');
            $disease_data = $this->getFarmDiseaseDataModel()->find($id);
            
            if (!$disease_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $disease_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }
            
            // Prepare update data
            $data = [
                'disease_type' => $this->request->getPost('disease_type'),
                'disease_name' => $this->request->getPost('disease_name'),
                'number_of_plants' => $this->request->getPost('number_of_plants'),
                'breed' => $this->request->getPost('breed'),
                'hectares' => $this->request->getPost('hectares'),
                'action_date' => $this->request->getPost('action_date'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Validate disease data
            $this->validateDiseaseData($data);

            // Update the record
            $this->getFarmDiseaseDataModel()->update($id, $data);

            // Update block disease status
            $this->updateBlockDiseaseStatus($disease_data['block_id']);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Disease data updated successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Disease Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Delete disease data with proper validation and error handling
     * @param int $id
     * @return \CodeIgniter\HTTP\Response
     */
    public function delete_disease_data($id)
    {
        try {
            $disease_data = $this->getFarmDiseaseDataModel()->find($id);
            
            if (!$disease_data) {
                throw new \Exception('Record not found');
            }
            
            // Verify block belongs to user's district
            $block = $this->getFarmBlockModel()->where('id', $disease_data['block_id'])
                ->where('district_id', session()->get('district_id'))
                ->first();
                
            if (!$block) {
                throw new \Exception('Access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->getFarmDiseaseDataModel()->update($id, $data);

            // Update block disease status after deletion
            $this->updateBlockDiseaseStatus($disease_data['block_id']);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Record deleted successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Delete Disease Data] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Prepare disease data from request
     * @param int $block_id
     * @return array
     */
    private function prepareDiseaseData(int $block_id): array
    {
        return [
            'block_id' => $block_id,
            'disease_type' => $this->request->getPost('disease_type'),
            'disease_name' => $this->request->getPost('disease_name'),
            'number_of_plants' => $this->request->getPost('number_of_plants'),
            'breed' => $this->request->getPost('breed'),
            'hectares' => $this->request->getPost('hectares'),
            'action_date' => $this->request->getPost('action_date'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => session()->get('emp_id'),
            'created_at' => date('Y-m-d H:i:s'),
            'status' => 'active'
        ];
    }

    /**
     * Validate disease data fields
     * @param array $data
     * @throws \Exception
     */
    private function validateDiseaseData(array $data): void
    {
        // Required fields validation
        $required = [
            'disease_type',
            'disease_name',
            'number_of_plants',
            'breed',
            'hectares',
            'action_date'
        ];
        $this->validateInput($data, $required);

        // Numeric fields validation
        $numericFields = [
            'number_of_plants' => 'Number of plants',
            'hectares' => 'Hectares'
        ];

        foreach ($numericFields as $field => $label) {
            if (!isset($data[$field])) continue;
            
            if (!is_numeric($data[$field])) {
                throw new \Exception("{$label} must be a number.");
            }
            
            if ($data[$field] <= 0) {
                throw new \Exception("{$label} must be a positive number.");
            }
        }

        // Validate action date
        if (strtotime($data['action_date']) > time()) {
            throw new \Exception("Action date cannot be in the future.");
        }
    }

    /**
     * Update block disease status based on active disease records
     * @param int $block_id
     */
    private function updateBlockDiseaseStatus(int $block_id): void
    {
        try {
            // Count active disease records
            $count = $this->getFarmDiseaseDataModel()
                ->where('block_id', $block_id)
                ->where('status', 'active')
                ->countAllResults();

            // Update block status
            $this->getFarmBlockModel()->update($block_id, [
                'has_disease' => ($count > 0) ? 1 : 0,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Update Block Disease Status] ' . $e->getMessage());
            // Don't throw exception here to prevent disrupting the main flow
        }
    }
}
