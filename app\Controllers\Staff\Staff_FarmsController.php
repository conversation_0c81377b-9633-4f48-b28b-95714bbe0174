<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\{
    AdxDistrictModel,
    FarmerInformationModel,
    CropsFarmBlockModel,
    CropsFarmCropsDataModel,
    AdxLlgModel,
    AdxProvinceModel,
    CropsModel,
    DakoiiUsersModel,
    AdxWardModel,
    LivestockFarmBlockModel,
    CropsFarmFertilizerDataModel,
    CropsFarmPesticidesDataModel,
    CropsFarmHarvestDataModel,
    CropsFarmMarketingDataModel,
    CropBuyersModel,
    PesticidesModel,
    FertilizersModel,
    CropsFarmDiseaseDataModel,
    InfectionsModel,
    CropsFarmBlockFilesModel
};

class Staff_FarmsController extends BaseController
{
    protected $models = [];
    protected $farmersModel;
    protected $farmBlockModel;
    protected $livestockFarmBlockModel;
    protected $farmCropsDataModel;
    protected $provincesModel;
    protected $districtsModel;
    protected $llgsModel;
    protected $wardsModel;
    protected $cropsModel;
    protected $farmFertilizerDataModel;
    protected $farmPesticidesDataModel;
    protected $farmHarvestDataModel;
    protected $farmMarketingDataModel;
    protected $cropBuyersModel;
    protected $pesticidesModel;
    protected $fertilizersModel;
    protected $farmDiseaseDataModel;
    protected $infectionsModel;
    protected $farmBlockFilesModel;

    public function __construct()
    {
        helper(['url', 'form', 'info', 'weather']);
        
        if (!session()->get('logged_in') || session()->get('role') !== 'user') {
            throw new \Exception('Unauthorized access');
        }

        // Initialize models
        $this->models = [
            'users' => new DakoiiUsersModel(),
            'farmers' => new FarmerInformationModel(),
            'farmBlock' => new CropsFarmBlockModel(),
            'livestockFarmBlock' => new LivestockFarmBlockModel(),
            'farmCropsData' => new CropsFarmCropsDataModel(),
            'provinces' => new AdxProvinceModel(),
            'districts' => new AdxDistrictModel(),
            'llgs' => new AdxLlgModel(),
            'wards' => new AdxWardModel(),
            'crops' => new CropsModel(),
            'farmFertilizer' => new CropsFarmFertilizerDataModel(),
            'farmPesticides' => new CropsFarmPesticidesDataModel(),
            'farmHarvest' => new CropsFarmHarvestDataModel(),
            'farmMarketing' => new CropsFarmMarketingDataModel(),
            'cropBuyers' => new CropBuyersModel(),
            'pesticides' => new PesticidesModel(),
            'fertilizers' => new FertilizersModel(),
            'farmDisease' => new CropsFarmDiseaseDataModel(),
            'infections' => new InfectionsModel(),
            'farmBlockFiles' => new CropsFarmBlockFilesModel()
        ];

        // Set model properties for backward compatibility
        $this->farmersModel = $this->models['farmers'];
        $this->farmBlockModel = $this->models['farmBlock'];
        $this->livestockFarmBlockModel = $this->models['livestockFarmBlock'];
        $this->farmCropsDataModel = $this->models['farmCropsData'];
        $this->provincesModel = $this->models['provinces'];
        $this->districtsModel = $this->models['districts'];
        $this->llgsModel = $this->models['llgs'];
        $this->wardsModel = $this->models['wards'];
        $this->cropsModel = $this->models['crops'];
        $this->farmFertilizerDataModel = $this->models['farmFertilizer'];
        $this->farmPesticidesDataModel = $this->models['farmPesticides'];
        $this->farmHarvestDataModel = $this->models['farmHarvest'];
        $this->farmMarketingDataModel = $this->models['farmMarketing'];
        $this->cropBuyersModel = $this->models['cropBuyers'];
        $this->pesticidesModel = $this->models['pesticides'];
        $this->fertilizersModel = $this->models['fertilizers'];
        $this->farmDiseaseDataModel = $this->models['farmDisease'];
        $this->infectionsModel = $this->models['infections'];
        $this->farmBlockFilesModel = $this->models['farmBlockFiles'];
    }

    protected function verifyDistrictAccess($districtId) 
    {
        return $districtId == session()->get('district_id');
    }

    protected function validateInput($data, $required = [])
    {
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new \Exception("The {$field} field is required.");
            }
        }

        array_walk_recursive($data, function(&$value) {
            $value = strip_tags($value);
            $value = trim($value);
        });

        return $data;
    }

    protected function validateNumericInput($value, $fieldName) 
    {
        if (!is_numeric($value)) {
            throw new \Exception("The {$fieldName} must be a numeric value.");
        }
    }

    protected function validateCoordinates($lon, $lat) 
    {
        if (!is_numeric($lon) || !is_numeric($lat)) {
            throw new \Exception('Coordinates must be numeric values.');
        }
        if ($lon < -180 || $lon > 180) {
            throw new \Exception('Longitude must be between -180 and 180.');
        }
        if ($lat < -90 || $lat > 90) {
            throw new \Exception('Latitude must be between -90 and 90.');
        }
    }

    public function getFarmersModel()
    {
        return $this->farmersModel ??= new FarmerInformationModel();
    }

    public function view()
    {
        $district = $this->districtsModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';
        
        $llgs = $this->llgsModel->where('district_id', session()->get('district_id'))->findAll();

        $data = [
            'title' => 'Farm Blocks',
            'page_header' => 'Farm Blocks',
            'farmers' => $this->getFarmersModel()->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'farm_blocks' => $this->farmBlockModel->select("
                crops_farm_blocks.id,
                crops_farm_blocks.block_code,
                crops_farm_blocks.farmer_id,
                crops_farm_blocks.crop_id,
                crops_farm_blocks.llg_id,
                crops_farm_blocks.ward_id,
                crops_farm_blocks.village,
                crops_farm_blocks.block_site,
                crops_farm_blocks.lon,
                crops_farm_blocks.lat,
                crops_farm_blocks.remarks,
                CONCAT(farmer_information.given_name, ' ', farmer_information.surname) AS farmer_name,
                adx_crops.crop_name,
                adx_ward.name as ward_name,
                adx_llg.name as llg_name
            ")
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id', 'left')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id', 'left')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id', 'left')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id', 'left')
            ->where('crops_farm_blocks.district_id', session()->get('district_id'))
            ->where('crops_farm_blocks.status', 'active')
            ->findAll(),
            'district_name' => $districtName,
            'llgs' => $llgs,
            'crops' => $this->cropsModel->findAll(),
        ];

        return view('staff/farms/farm_blocks', $data);
    }

    public function farm_blocks($farmer_id = null)
    {
        $pager = service('pager');
        $page = (int)(request()->getGet('page') ?? 1);
        $perPage = 20;

        $data = [
            'title' => 'Farm Blocks',
            'page_header' => 'Farm Blocks',
            'farmer' => $farmer_id ? $this->getFarmersModel()->find($farmer_id) : null,
            'farmers' => $this->getFarmersModel()->where('status', 'active')
                ->where('district_id', session()->get('district_id'))
                ->findAll(),
            'farm_blocks' => $farmer_id ? $this->farmBlockModel->where('farmer_id', $farmer_id)
                ->where('district_id', session()->get('district_id'))
                ->orderBy('id', 'asc')
                ->paginate($perPage, 'default', $page) : null,
            'districts' => $this->districtsModel->where('province_id', session()->get('orgprovince_id'))
                ->findAll(),
            'pager' => $this->farmBlockModel->pager
        ];

        return view('staff/farms/farm_blocks', $data);
    }

    public function add_farm_block()
    {
        helper('form'); // Only load here if needed
        try {
            $farmer_id = $this->request->getPost('farmer_id');
            $lon = $this->request->getPost('lon');
            $lat = $this->request->getPost('lat');

            // Validate coordinates if provided
            if ($lon !== '' && $lat !== '') {
                $this->validateCoordinates($lon, $lat);
            }

            $latest_block_code = $this->farmBlockModel->where('farmer_id', $farmer_id)
                ->orderBy('id', 'DESC')
                ->first();
            $farmer_code = $this->getFarmersModel()->find($farmer_id)['farmer_code'];

            if ($latest_block_code) {
                $current_number = (int)substr($latest_block_code['block_code'], strpos($latest_block_code['block_code'], '-') + 1);
                $next_block_code = $farmer_code . '-' . sprintf('%03d', $current_number + 1);
            } else {
                $next_block_code = $farmer_code . '-001';
            }

            $data = [
                'farmer_id' => $farmer_id,
                'crop_id' => $this->request->getPost('crop_id'),
                'block_code' => $next_block_code,
                'org_id' => session()->get('org_id'),
                'country_id' => session()->get('orgcountry_id'),
                'province_id' => session()->get('orgprovince_id'),
                'district_id' => session()->get('district_id'),
                'llg_id' => $this->request->getPost('llg_id'),
                'ward_id' => $this->request->getPost('ward_id'),
                'village' => $this->request->getPost('village'),
                'block_site' => $this->request->getPost('block_site'),
                'lon' => $lon,
                'lat' => $lat,
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('emp_id'),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            // Validate required fields
            $required = ['farmer_id', 'crop_id', 'llg_id', 'ward_id'];
            $this->validateInput($data, $required);

            $this->farmBlockModel->save($data);
            return $this->response->setJSON([
                'status' => 'success', 
                'message' => 'Farm block added successfully!'
            ]);
        } catch (\Exception $e) {
            log_message('error', '[Add Farm Block] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error', 
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update_farm_block()
    {
        try {
            $block_id = $this->request->getPost('block_id');
            $existing_block = $this->farmBlockModel->where('id', $block_id)->where('district_id', session()->get('district_id'))->first();

            if (!$existing_block) {
                throw new \Exception('Farm block not found or access denied');
            }

            $farmer_id = $this->request->getPost('farmer_id');
            $farmer_code = $this->getFarmersModel()->find($farmer_id)['farmer_code'];

            // Regenerate block code if farmer changed
            if (substr($existing_block['block_code'], 0, strpos($existing_block['block_code'], '-')) !== $farmer_code) {
                $latest_block_code = $this->farmBlockModel->where('farmer_id', $farmer_id)->orderBy('id', 'DESC')->first();
                if ($latest_block_code) {
                    $current_number = (int)substr($latest_block_code['block_code'], strpos($latest_block_code['block_code'], '-') + 1);
                    $next_block_code = $farmer_code . '-' . sprintf('%03d', $current_number + 1);
                } else {
                    $next_block_code = $farmer_code . '-001';
                }
            } else {
                $next_block_code = $existing_block['block_code'];
            }

            $data = [
                'block_code' => $next_block_code,
                'farmer_id' => $farmer_id,
                'crop_id' => $this->request->getPost('crop_id'),
                'llg_id' => $this->request->getPost('llg_id'),
                'ward_id' => $this->request->getPost('ward_id'),
                'village' => $this->request->getPost('village'),
                'block_site' => $this->request->getPost('block_site'),
                'lon' => $this->request->getPost('lon'),
                'lat' => $this->request->getPost('lat'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmBlockModel->update($block_id, $data);
            return $this->response->setJSON(['status' => 'success', 'message' => 'Farm block updated successfully!']);
        } catch (\Exception $e) {
            log_message('error', '[Update Farm Block] ' . $e->getMessage());
            return $this->response->setJSON(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    public function delete_farm_block($id)
    {
        try {
            // Verify block belongs to user's district
            $block = $this->farmBlockModel->where('id', $id)
                ->where('district_id', session()->get('district_id'))
                ->first();
            
            if (!$block) {
                throw new \Exception('Block not found or access denied');
            }

            $data = [
                'status' => 'deleted',
                'updated_by' => session()->get('emp_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->farmBlockModel->update($id, $data);

            return redirect()->back()->with('success', 'Farm block deleted successfully!');
        } catch (\Exception $e) {
            log_message('error', '[Delete Farm Block] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while deleting the farm block');
        }
    }

    public function get_llgs()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $districtId = $this->request->getPost('district_id');
        if (!$districtId) {
            return $this->response->setJSON(['success' => false, 'message' => 'District ID is required']);
        }

        try {
            $llgs = $this->llgsModel->where('district_id', $districtId)->findAll();
            return $this->response->setJSON(['success' => true, 'llgs' => $llgs])
                ->setHeader('Cache-Control', 'public, max-age=86400');
        } catch (\Exception $e) {
            log_message('error', 'Error in get_llgs: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'message' => 'Database error occurred']);
        }
    }

    public function get_wards()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $llg_id = $this->request->getPost('llg_id');
        if (!$llg_id) {
            return $this->response->setJSON(['success' => false, 'message' => 'LLG ID is required']);
        }

        try {
            $wards = $this->wardsModel->where('llg_id', $llg_id)->findAll();
            return $this->response->setJSON(['success' => true, 'wards' => $wards]);
        } catch (\Exception $e) {
            log_message('error', 'Error in get_wards: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'message' => 'Database error occurred']);
        }
    }

    public function maps()
    {
        $district = $this->districtsModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Field Maps',
            'page_header' => 'Field Maps',
            'farm_blocks' => $this->farmBlockModel->getFarmBlocksWithDetails(session()->get('district_id')),
            'district_name' => $districtName,
            'crops' => $this->cropsModel->findAll(),
            'livestock_blocks' => []  // Add empty array for livestock blocks if needed
        ];

        return view('staff/farms/maps', $data);
    }

    /**
     * Upload files for a farm block
     */
    public function upload_farm_block_file()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $farm_block_id = $this->request->getPost('farm_block_id');
        $file_caption = $this->request->getPost('file_caption');
        
        if (!$farm_block_id) {
            return $this->response->setJSON(['success' => false, 'message' => 'Farm block ID is required']);
        }
        
        if (!$file_caption) {
            return $this->response->setJSON(['success' => false, 'message' => 'File caption is required']);
        }
        
        // Check if farm block exists
        $farmBlock = $this->farmBlockModel->find($farm_block_id);
        if (!$farmBlock) {
            return $this->response->setJSON(['success' => false, 'message' => 'Farm block not found']);
        }
        
        // Handle file upload
        $file = $this->request->getFile('file');
        
        if (!$file->isValid() || $file->hasMoved()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid file or file already moved']);
        }
        
        try {
            // Create uploads directory if it doesn't exist
            $uploadPath = FCPATH . 'uploads/farm_blocks';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }
            
            // Generate a new file name to avoid conflicts
            $newName = $file->getRandomName();
            
            // Move the file to the upload directory
            $file->move($uploadPath, $newName);
            
            // Save file information to database
            $fileData = [
                'farm_block_id' => $farm_block_id,
                'file_caption' => $file_caption,
                'file_path' => 'uploads/farm_blocks/' . $newName,
                'uploaded_by' => session()->get('user_id'),
                'uploaded_at' => date('Y-m-d H:i:s')
            ];
            
            $this->farmBlockFilesModel->insert($fileData);
            
            return $this->response->setJSON([
                'success' => true, 
                'message' => 'File uploaded successfully',
                'file_id' => $this->farmBlockFilesModel->getInsertID()
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error in upload_farm_block_file: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'message' => 'Error uploading file: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Get files for a farm block
     */
    public function get_farm_block_files($farm_block_id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }
        
        if (!$farm_block_id) {
            return $this->response->setJSON(['success' => false, 'message' => 'Farm block ID is required']);
        }
        
        try {
            $files = $this->farmBlockFilesModel->where('farm_block_id', $farm_block_id)->findAll();
            
            return $this->response->setJSON([
                'success' => true,
                'files' => $files
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error in get_farm_block_files: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'message' => 'Error retrieving files: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Delete a farm block file
     */
    public function delete_farm_block_file($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }
        
        if (!$id) {
            return $this->response->setJSON(['success' => false, 'message' => 'File ID is required']);
        }
        
        try {
            // Get file information
            $file = $this->farmBlockFilesModel->find($id);
            
            if (!$file) {
                return $this->response->setJSON(['success' => false, 'message' => 'File not found']);
            }
            
            // Delete file from storage
            $filePath = FCPATH . $file['file_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            
            // Delete record from database
            $this->farmBlockFilesModel->delete($id);
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'File deleted successfully'
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error in delete_farm_block_file: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'message' => 'Error deleting file: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Update farm block file caption
     */
    public function update_farm_block_file()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }
        
        $file_id = $this->request->getPost('file_id');
        $file_caption = $this->request->getPost('file_caption');
        
        if (!$file_id) {
            return $this->response->setJSON(['success' => false, 'message' => 'File ID is required']);
        }
        
        if (!$file_caption) {
            return $this->response->setJSON(['success' => false, 'message' => 'File caption is required']);
        }
        
        try {
            // Check if file exists
            $file = $this->farmBlockFilesModel->find($file_id);
            
            if (!$file) {
                return $this->response->setJSON(['success' => false, 'message' => 'File not found']);
            }
            
            // Update file caption
            $this->farmBlockFilesModel->update($file_id, [
                'file_caption' => $file_caption,
                'updated_by' => session()->get('user_id'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            return $this->response->setJSON([
                'success' => true,
                'message' => 'File caption updated successfully'
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Error in update_farm_block_file: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'message' => 'Error updating file: ' . $e->getMessage()]);
        }
    }
} 