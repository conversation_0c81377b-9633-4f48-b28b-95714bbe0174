<?php

namespace App\Models;

use CodeIgniter\Model;

class CropProcessorsModel extends Model
{
    protected $table = 'crop_processors';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    
    protected $allowedFields = [
        'crop_id',
        'stage',
        'processor_name',
        'description'
    ];

    // Validation rules
    protected $validationRules = [
       
    ];

    protected $validationMessages = [
        
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    /**
     * Get processors by crop ID
     *
     * @param int $cropId
     * @return array
     */
    public function getProcessorsByCrop($cropId)
    {
        return $this->where('crop_id', $cropId)->findAll();
    }

    /**
     * Get processors by stage
     *
     * @param string $stage
     * @return array
     */
    public function getProcessorsByStage($stage)
    {
        return $this->where('stage', $stage)->findAll();
    }

    /**
     * Get processor with crop details
     *
     * @param int $id
     * @return array|null
     */
    public function getProcessorWithCrop($id)
    {
        return $this->select('crop_processors.*, adx_crops.crop_name as crop_name')
                    ->join('adx_crops', 'adx_crops.id = crop_processors.crop_id')
                    ->find($id);
    }
} 