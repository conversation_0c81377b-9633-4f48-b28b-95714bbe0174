<?php

namespace App\Models;

use CodeIgniter\Model;

class FarmerInformationModel extends Model
{
    protected $table            = 'farmer_information';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'farmer_code',
        'given_name',
        'surname',
        'date_of_birth',
        'gender',
        'village',
        'ward_id',
        'llg_id',
        'district_id',
        'province_id',
        'country_id',
        'phone',
        'email',
        'address',
        'marital_status',
        'highest_education_id',
        'course_taken',
        'id_photo',
        'created_by',
        'updated_by',
        'deleted_by',
        'status'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'farmer_code' => 'permit_empty|max_length[20]|is_unique[farmer_information.farmer_code,id,{id}]',
        'given_name' => 'required|min_length[2]|max_length[50]|alpha_space',
        'surname' => 'required|min_length[2]|max_length[50]|alpha_space',
        'date_of_birth' => 'required|valid_date',
        'gender' => 'required|in_list[Male,Female]',
        'village' => 'permit_empty|max_length[100]',
        'ward_id' => 'required|numeric',
        'llg_id' => 'required|numeric',
        'district_id' => 'required|numeric',
        'province_id' => 'required|numeric',
        'country_id' => 'required|numeric',
        'phone' => 'permit_empty|max_length[200]',
        'email' => 'permit_empty|valid_email|max_length[255]',
        'address' => 'permit_empty|max_length[255]',
        'marital_status' => 'required|in_list[Single,Married,Divorce,Widow,De-facto]',
        'highest_education_id' => 'permit_empty|numeric',
        'course_taken' => 'permit_empty|max_length[255]',
        'id_photo' => 'permit_empty|max_length[255]',
        'status' => 'permit_empty|in_list[active,inactive]'
    ];

    protected $validationMessages = [
        'farmer_code' => [
            'max_length' => 'Farmer code cannot exceed 20 characters',
            'is_unique' => 'This farmer code already exists'
        ],
        'given_name' => [
            'required' => 'Given name is required',
            'min_length' => 'Given name must be at least 2 characters long',
            'max_length' => 'Given name cannot exceed 50 characters',
            'alpha_space' => 'Given name can only contain letters and spaces'
        ],
        'surname' => [
            'required' => 'Surname is required',
            'min_length' => 'Surname must be at least 2 characters long',
            'max_length' => 'Surname cannot exceed 50 characters',
            'alpha_space' => 'Surname can only contain letters and spaces'
        ],
        'date_of_birth' => [
            'required' => 'Date of birth is required',
            'valid_date' => 'Please enter a valid date'
        ],
        'gender' => [
            'required' => 'Gender is required',
            'in_list' => 'Gender must be Male or Female'
        ],
        'marital_status' => [
            'required' => 'Marital status is required',
            'in_list' => 'Please select a valid marital status'
        ],
        'llg_id' => [
            'required' => 'LLG selection is required',
            'numeric' => 'Invalid LLG selection'
        ],
        'ward_id' => [
            'required' => 'Ward selection is required',
            'numeric' => 'Invalid Ward selection'
        ],
        'district_id' => [
            'required' => 'District is required',
            'numeric' => 'Invalid District'
        ],
        'province_id' => [
            'required' => 'Province is required',
            'numeric' => 'Invalid Province'
        ],
        'country_id' => [
            'required' => 'Country is required',
            'numeric' => 'Invalid Country'
        ]
    ];

    /**
     * Generate unique farmer code
     * Format: F7 + province_code + increment (with leading zeros)
     * Example: F71401 (F7 + 14 + 01)
     */
    public function generateFarmerCode($provinceId)
    {
        // Get province code
        $provinceModel = new \App\Models\AdxProvinceModel();
        $province = $provinceModel->find($provinceId);

        if (!$province) {
            throw new \Exception('Province not found');
        }

        $provinceCode = $province['provincecode'];

        // Get the next increment number for this province
        $lastFarmer = $this->where('province_id', $provinceId)
                          ->where('farmer_code LIKE', "F7{$provinceCode}%")
                          ->orderBy('farmer_code', 'DESC')
                          ->first();

        $increment = 1;
        if ($lastFarmer) {
            // Extract increment from last farmer code
            $lastCode = $lastFarmer['farmer_code'];
            $lastIncrement = (int)substr($lastCode, strlen("F7{$provinceCode}"));
            $increment = $lastIncrement + 1;
        }

        // Format increment with leading zeros (minimum 2 digits)
        $formattedIncrement = str_pad($increment, 2, '0', STR_PAD_LEFT);

        // Generate farmer code: F7 + province_code + increment
        $farmerCode = "F7{$provinceCode}{$formattedIncrement}";

        // Ensure uniqueness (in case of race conditions)
        while ($this->where('farmer_code', $farmerCode)->first()) {
            $increment++;
            $formattedIncrement = str_pad($increment, 2, '0', STR_PAD_LEFT);
            $farmerCode = "F7{$provinceCode}{$formattedIncrement}";
        }

        return $farmerCode;
    }

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['generateUniqueFarmerCode', 'setDefaultStatus', 'setCreatedBy'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['setUpdatedBy'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = ['setDeletedBy'];
    protected $afterDelete    = [];

    protected function generateUniqueFarmerCode(array $data)
    {
        if (!isset($data['data']['farmer_code']) || empty($data['data']['farmer_code'])) {
            do {
                $code = 'F' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
                $exists = $this->where('farmer_code', $code)->first();
            } while ($exists);

            $data['data']['farmer_code'] = $code;
        }
        return $data;
    }

    protected function setDefaultStatus(array $data)
    {
        if (!isset($data['data']['status']) || empty($data['data']['status'])) {
            $data['data']['status'] = 'active';
        }
        return $data;
    }

    protected function setCreatedBy(array $data)
    {
        if (session()->has('emp_id')) {
            $data['data']['created_by'] = session()->get('emp_id');
        }
        return $data;
    }

    protected function setUpdatedBy(array $data)
    {
        if (session()->has('emp_id')) {
            $data['data']['updated_by'] = session()->get('emp_id');
        }
        return $data;
    }

    protected function setDeletedBy(array $data)
    {
        if (session()->has('emp_id')) {
            $data['data']['deleted_by'] = session()->get('emp_id');
        }
        return $data;
    }

    /**
     * Get farmer's full name
     */
    public function getFullName($id)
    {
        $farmer = $this->find($id);
        if ($farmer) {
            return trim($farmer['given_name'] . ' ' . $farmer['surname']);
        }
        return '';
    }

    /**
     * Get farmers with location details
     */
    public function getFarmersWithLocation($filters = [])
    {
        $builder = $this->select('farmer_information.*,
                                 adx_district.name as district_name,
                                 adx_llg.name as llg_name,
                                 adx_ward.name as ward_name,
                                 adx_province.name as province_name,
                                 adx_country.name as country_name')
                        ->join('adx_district', 'adx_district.id = farmer_information.district_id', 'left')
                        ->join('adx_llg', 'adx_llg.id = farmer_information.llg_id', 'left')
                        ->join('adx_ward', 'adx_ward.id = farmer_information.ward_id', 'left')
                        ->join('adx_province', 'adx_province.id = farmer_information.province_id', 'left')
                        ->join('adx_country', 'adx_country.id = farmer_information.country_id', 'left');

        // Apply filters
        if (!empty($filters['district_id'])) {
            $builder->where('farmer_information.district_id', $filters['district_id']);
        }

        if (!empty($filters['province_id'])) {
            $builder->where('farmer_information.province_id', $filters['province_id']);
        }

        if (!empty($filters['status'])) {
            $builder->where('farmer_information.status', $filters['status']);
        }

        return $builder->orderBy('farmer_information.created_at', 'DESC')->findAll();
    }

    /**
     * Search farmers by name or farmer code
     */
    public function searchFarmers($searchTerm, $filters = [])
    {
        $builder = $this->getFarmersWithLocation($filters);

        if (!empty($searchTerm)) {
            $builder->groupStart()
                   ->like('farmer_information.given_name', $searchTerm)
                   ->orLike('farmer_information.surname', $searchTerm)
                   ->orLike('farmer_information.farmer_code', $searchTerm)
                   ->groupEnd();
        }

        return $builder;
    }
}
