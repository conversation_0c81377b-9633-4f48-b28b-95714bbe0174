<?php

namespace App\Models;

use CodeIgniter\Model;

class LivestockModel extends Model
{
    protected $table            = 'adx_livestock';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'name',
        'icon',
        'color_code',
        'remarks',
        'created_by',
        'updated_by',
        'status'
    ];

    // Timestamps
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Skip validation
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Validation Rules
    protected $validationRules = [
        'name' => 'required|min_length[2]|max_length[100]',
        'color_code' => 'permit_empty|max_length[50]',
        'status' => 'permit_empty|in_list[0,1]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Livestock name is required',
            'min_length' => 'Livestock name must be at least 2 characters long',
            'max_length' => 'Livestock name cannot exceed 100 characters'
        ],
        'color_code' => [
            'max_length' => 'Color code cannot exceed 50 characters'
        ],
        'status' => [
            'in_list' => 'Invalid status value'
        ]
    ];

    // Helper methods
    public function getLivestock()
    {
        return $this->where('status', 1)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    public function getLivestockById($id)
    {
        return $this->find($id);
    }

    public function getLivestockName($id)
    {
        $livestock = $this->getLivestockById($id);
        return $livestock ? $livestock['name'] : null;
    }

    public function getLivestockIcon($id)
    {
        $livestock = $this->getLivestockById($id);
        return $livestock ? $livestock['icon'] : null;
    }

    public function getLivestockColorCode($id)
    {
        $livestock = $this->getLivestockById($id);
        return $livestock ? $livestock['color_code'] : null;
    }
    

    public function getLivestockInfo($identifier)
    {
        return $this->find($identifier);
    }

    public function getIdFromValue($value)
    {
        return $value;  // In new system, value is the ID
    }

    // Get active livestock
    public function getActiveLivestock()
    {
        return $this->where('status', 1)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    // Get livestock with details
    public function getLivestockWithDetails()
    {
        return $this->select('adx_livestock.*, users.name as created_by_name')
                    ->join('users', 'users.id = adx_livestock.created_by', 'left')
                    ->orderBy('adx_livestock.name', 'ASC')
                    ->findAll();
    }

    // Get active livestock with details
    public function getActiveLivestockWithDetails()
    {
        return $this->select('
                adx_livestock.*,
                COALESCE(
                    (SELECT SUM(he_total + she_total)
                    FROM livestock_farm_data
                    WHERE livestock_farm_data.livestock_id = adx_livestock.id
                    AND livestock_farm_data.status = "active"), 0
                ) as total_count,
                COALESCE(
                    (SELECT COUNT(DISTINCT block_id)
                    FROM livestock_farm_data
                    WHERE livestock_farm_data.livestock_id = adx_livestock.id
                    AND livestock_farm_data.status = "active"), 0
                ) as total_blocks
            ')
            ->where('adx_livestock.status', 1)
            ->orderBy('adx_livestock.name', 'ASC')
            ->findAll();
    }
}