<?php

namespace App\Models;

use CodeIgniter\Model;

class PermissionsItemsModel extends Model
{
    protected $table            = 'permissions_items';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;

    protected $allowedFields = [
        'permission_code',
        'permission_text',
        'created_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = '';

    protected $validationRules = [
        'permission_code' => 'required|min_length[2]|max_length[255]|is_unique[permissions_items.permission_code]',
        'permission_text' => 'required|min_length[3]',
        'created_by'      => 'required|max_length[255]'
    ];

    protected $validationMessages = [
        'permission_code' => [
            'required'   => 'Permission code is required',
            'min_length' => 'Permission code must be at least 2 characters long',
            'max_length' => 'Permission code cannot exceed 255 characters',
            'is_unique'  => 'Permission code already exists'
        ],
        'permission_text' => [
            'required'   => 'Permission text is required',
            'min_length' => 'Permission text must be at least 3 characters long'
        ],
        'created_by' => [
            'required'   => 'Created by is required',
            'max_length' => 'Created by cannot exceed 255 characters'
        ]
    ];

    protected $skipValidation     = false;
    protected $cleanValidationRules = true;

    /**
     * Get permission by code
     *
     * @param string $code
     * @return array|null
     */
    public function getByCode($code)
    {
        return $this->where('permission_code', $code)->first();
    }

    /**
     * Get all permissions as dropdown options
     *
     * @return array
     */
    public function getDropdownOptions()
    {
        $permissions = $this->orderBy('permission_text', 'ASC')->findAll();
        $options = [];
        
        foreach ($permissions as $permission) {
            $options[$permission['id']] = $permission['permission_text'];
        }
        
        return $options;
    }

    /**
     * Check if permission code exists
     *
     * @param string $code
     * @param int|null $excludeId
     * @return bool
     */
    public function codeExists($code, $excludeId = null)
    {
        $builder = $this->where('permission_code', $code);
        
        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }
        
        return $builder->countAllResults() > 0;
    }

    /**
     * Get permissions with usage count
     *
     * @return array
     */
    public function getPermissionsWithUsageCount()
    {
        return $this->select('permissions_items.*, 
                             COUNT(permissions_sets.id) as usage_count')
                    ->join('permissions_sets', 'permissions_sets.permission_id = permissions_items.id', 'left')
                    ->groupBy('permissions_items.id')
                    ->orderBy('permissions_items.permission_text', 'ASC')
                    ->findAll();
    }
}
