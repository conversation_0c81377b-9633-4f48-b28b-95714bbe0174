<?php

namespace App\Models;

use CodeIgniter\Model;

class PermissionsSetsModel extends Model
{
    protected $table            = 'permissions_sets';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;

    protected $allowedFields = [
        'permission_id',
        'user_id',
        'created_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = '';

    protected $validationRules = [
        'permission_id' => 'required|integer',
        'user_id'       => 'required|integer',
        'created_by'    => 'required|max_length[255]'
    ];

    protected $validationMessages = [
        'permission_id' => [
            'required' => 'Permission ID is required',
            'integer'  => 'Permission ID must be a valid integer'
        ],
        'user_id' => [
            'required' => 'User ID is required',
            'integer'  => 'User ID must be a valid integer'
        ],
        'created_by' => [
            'required'   => 'Created by is required',
            'max_length' => 'Created by cannot exceed 255 characters'
        ]
    ];

    protected $skipValidation     = false;
    protected $cleanValidationRules = true;

    /**
     * Get user permissions with permission details
     *
     * @param int $userId
     * @return array
     */
    public function getUserPermissions($userId)
    {
        return $this->select('permissions_sets.*, 
                             permissions_items.permission_code,
                             permissions_items.permission_text')
                    ->join('permissions_items', 'permissions_items.id = permissions_sets.permission_id')
                    ->where('permissions_sets.user_id', $userId)
                    ->orderBy('permissions_items.permission_text', 'ASC')
                    ->findAll();
    }

    /**
     * Check if user has specific permission
     *
     * @param int $userId
     * @param string $permissionCode
     * @return bool
     */
    public function userHasPermission($userId, $permissionCode)
    {
        return $this->join('permissions_items', 'permissions_items.id = permissions_sets.permission_id')
                    ->where('permissions_sets.user_id', $userId)
                    ->where('permissions_items.permission_code', $permissionCode)
                    ->countAllResults() > 0;
    }

    /**
     * Add permission to user
     *
     * @param int $userId
     * @param int $permissionId
     * @param string $createdBy
     * @return bool
     */
    public function addUserPermission($userId, $permissionId, $createdBy)
    {
        // Check if permission already exists
        if ($this->where(['user_id' => $userId, 'permission_id' => $permissionId])->first()) {
            return false; // Permission already exists
        }

        return $this->insert([
            'user_id' => $userId,
            'permission_id' => $permissionId,
            'created_by' => $createdBy
        ]);
    }

    /**
     * Remove permission from user
     *
     * @param int $userId
     * @param int $permissionId
     * @return bool
     */
    public function removeUserPermission($userId, $permissionId)
    {
        return $this->where(['user_id' => $userId, 'permission_id' => $permissionId])->delete();
    }

    /**
     * Remove all permissions from user
     *
     * @param int $userId
     * @return bool
     */
    public function removeAllUserPermissions($userId)
    {
        return $this->where('user_id', $userId)->delete();
    }

    /**
     * Get users with specific permission
     *
     * @param string $permissionCode
     * @return array
     */
    public function getUsersWithPermission($permissionCode)
    {
        return $this->select('permissions_sets.*, 
                             users.name as user_name,
                             users.email as user_email')
                    ->join('permissions_items', 'permissions_items.id = permissions_sets.permission_id')
                    ->join('users', 'users.id = permissions_sets.user_id')
                    ->where('permissions_items.permission_code', $permissionCode)
                    ->orderBy('users.name', 'ASC')
                    ->findAll();
    }

    /**
     * Sync user permissions
     *
     * @param int $userId
     * @param array $permissionIds
     * @param string $createdBy
     * @return bool
     */
    public function syncUserPermissions($userId, $permissionIds, $createdBy)
    {
        // Remove all existing permissions
        $this->removeAllUserPermissions($userId);

        // Add new permissions
        foreach ($permissionIds as $permissionId) {
            $this->addUserPermission($userId, $permissionId, $createdBy);
        }

        return true;
    }
}
