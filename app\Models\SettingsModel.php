<?php

namespace App\Models;

use CodeIgniter\Model;

class SettingsModel extends Model
{
    protected $table            = 'settings';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;

    protected $allowedFields = [
        'value',
        'name'
    ];

    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'create_at'; // Note: database has 'create_at' not 'created_at'
    protected $updatedField  = '';

    protected $validationRules = [
        'name'  => 'required|min_length[2]|max_length[200]',
        'value' => 'required|max_length[200]'
    ];

    protected $validationMessages = [
        'name' => [
            'required'   => 'Setting name is required',
            'min_length' => 'Setting name must be at least 2 characters long',
            'max_length' => 'Setting name cannot exceed 200 characters'
        ],
        'value' => [
            'required'   => 'Setting value is required',
            'max_length' => 'Setting value cannot exceed 200 characters'
        ]
    ];

    protected $skipValidation     = false;
    protected $cleanValidationRules = true;

    /**
     * Get setting by name
     *
     * @param string $name
     * @return array|null
     */
    public function getByName($name)
    {
        return $this->where('name', $name)->first();
    }

    /**
     * Get setting value by name
     *
     * @param string $name
     * @param mixed $default
     * @return mixed
     */
    public function getValue($name, $default = null)
    {
        $setting = $this->getByName($name);
        return $setting ? $setting['value'] : $default;
    }

    /**
     * Set setting value
     *
     * @param string $name
     * @param string $value
     * @return bool
     */
    public function setValue($name, $value)
    {
        $existing = $this->getByName($name);
        
        if ($existing) {
            return $this->update($existing['id'], ['value' => $value]);
        } else {
            return $this->insert(['name' => $name, 'value' => $value]);
        }
    }

    /**
     * Get all settings as key-value pairs
     *
     * @return array
     */
    public function getAllAsKeyValue()
    {
        $settings = $this->findAll();
        $result = [];
        
        foreach ($settings as $setting) {
            $result[$setting['name']] = $setting['value'];
        }
        
        return $result;
    }
}
