<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_header ?></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Admin Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('admin/users') ?>">User Management</a></li>
                    <li class="breadcrumb-item active">Create User</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <form action="<?= base_url('admin/users') ?>" method="POST" enctype="multipart/form-data">
            <div class="row">
                <!-- User Information -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">User Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="name">Full Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?= old('name') ?>" required>
                                        <?php if (isset($errors['name'])): ?>
                                            <small class="text-danger"><?= $errors['name'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?= old('email') ?>">
                                        <?php if (isset($errors['email'])): ?>
                                            <small class="text-danger"><?= $errors['email'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="phone">Phone Number</label>
                                        <input type="text" class="form-control" id="phone" name="phone" 
                                               value="<?= old('phone') ?>">
                                        <?php if (isset($errors['phone'])): ?>
                                            <small class="text-danger"><?= $errors['phone'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="position">Position/Title</label>
                                        <input type="text" class="form-control" id="position" name="position" 
                                               value="<?= old('position') ?>">
                                        <?php if (isset($errors['position'])): ?>
                                            <small class="text-danger"><?= $errors['position'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="password">Password <span class="text-danger">*</span></label>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <?php if (isset($errors['password'])): ?>
                                            <small class="text-danger"><?= $errors['password'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="role">User Role <span class="text-danger">*</span></label>
                                        <select class="form-control" id="role" name="role" required>
                                            <option value="">Select Role</option>
                                            <option value="user" <?= old('role') == 'user' ? 'selected' : '' ?>>Field User</option>
                                            <option value="guest" <?= old('role') == 'guest' ? 'selected' : '' ?>>Guest</option>
                                        </select>
                                        <?php if (isset($errors['role'])): ?>
                                            <small class="text-danger"><?= $errors['role'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin" value="1"
                                                   <?= old('is_admin') ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_admin">
                                                Admin Privileges
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="is_supervisor" name="is_supervisor" value="1"
                                                   <?= old('is_supervisor') ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_supervisor">
                                                Supervisor Privileges
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="id_photo">Profile Photo</label>
                                <input type="file" class="form-control-file" id="id_photo" name="id_photo" accept="image/*">
                                <small class="form-text text-muted">Upload a profile photo (optional)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Permissions & Districts -->
                <div class="col-md-4">
                    <!-- District Permissions -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">District Access</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label>Assigned Districts <span class="text-danger">*</span></label>
                                <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                    <?php foreach ($districts as $district): ?>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input district-checkbox" 
                                               id="district_<?= $district['id'] ?>" 
                                               name="districts[]" 
                                               value="<?= $district['id'] ?>"
                                               <?= in_array($district['id'], old('districts') ?: []) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="district_<?= $district['id'] ?>">
                                            <?= esc($district['name']) ?>
                                        </label>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php if (isset($errors['districts'])): ?>
                                    <small class="text-danger"><?= $errors['districts'] ?></small>
                                <?php endif; ?>
                            </div>

                            <div class="form-group">
                                <label for="default_district">Default District <span class="text-danger">*</span></label>
                                <select class="form-control" id="default_district" name="default_district" required>
                                    <option value="">Select Default District</option>
                                    <?php foreach ($districts as $district): ?>
                                    <option value="<?= $district['id'] ?>" 
                                            <?= old('default_district') == $district['id'] ? 'selected' : '' ?>>
                                        <?= esc($district['name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['default_district'])): ?>
                                    <small class="text-danger"><?= $errors['default_district'] ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- System Permissions -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">System Permissions</h3>
                        </div>
                        <div class="card-body">
                            <div style="max-height: 300px; overflow-y: auto;">
                                <?php foreach ($permissions as $permission): ?>
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" 
                                           id="permission_<?= $permission['id'] ?>" 
                                           name="permissions[]" 
                                           value="<?= $permission['id'] ?>"
                                           <?= in_array($permission['id'], old('permissions') ?: []) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="permission_<?= $permission['id'] ?>">
                                        <?= esc($permission['permission_text']) ?>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> Create User
                            </button>
                            <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</section>

<script>
// Update default district dropdown when districts are selected/deselected
document.addEventListener('DOMContentLoaded', function() {
    const districtCheckboxes = document.querySelectorAll('.district-checkbox');
    const defaultDistrictSelect = document.getElementById('default_district');
    
    function updateDefaultDistrictOptions() {
        const selectedDistricts = Array.from(districtCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => ({
                value: cb.value,
                text: cb.nextElementSibling.textContent.trim()
            }));
        
        // Clear current options except the first one
        defaultDistrictSelect.innerHTML = '<option value="">Select Default District</option>';
        
        // Add options for selected districts
        selectedDistricts.forEach(district => {
            const option = document.createElement('option');
            option.value = district.value;
            option.textContent = district.text;
            defaultDistrictSelect.appendChild(option);
        });
    }
    
    districtCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateDefaultDistrictOptions);
    });
    
    // Initialize on page load
    updateDefaultDistrictOptions();
});
</script>

<?= $this->endSection() ?>
