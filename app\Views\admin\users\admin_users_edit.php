<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_header ?></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Admin Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('admin/users') ?>">User Management</a></li>
                    <li class="breadcrumb-item active">Edit User</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <form action="<?= base_url('admin/users/' . $user['id']) ?>" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="_method" value="PUT">
            
            <div class="row">
                <!-- User Information -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">User Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="name">Full Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?= old('name', $user['name']) ?>" required>
                                        <?php if (isset($errors['name'])): ?>
                                            <small class="text-danger"><?= $errors['name'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?= old('email', $user['email']) ?>">
                                        <?php if (isset($errors['email'])): ?>
                                            <small class="text-danger"><?= $errors['email'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="phone">Phone Number</label>
                                        <input type="text" class="form-control" id="phone" name="phone" 
                                               value="<?= old('phone', $user['phone']) ?>">
                                        <?php if (isset($errors['phone'])): ?>
                                            <small class="text-danger"><?= $errors['phone'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="position">Position/Title</label>
                                        <input type="text" class="form-control" id="position" name="position" 
                                               value="<?= old('position', $user['position']) ?>">
                                        <?php if (isset($errors['position'])): ?>
                                            <small class="text-danger"><?= $errors['position'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="password">New Password</label>
                                        <input type="password" class="form-control" id="password" name="password">
                                        <small class="form-text text-muted">Leave blank to keep current password</small>
                                        <?php if (isset($errors['password'])): ?>
                                            <small class="text-danger"><?= $errors['password'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="role">User Role <span class="text-danger">*</span></label>
                                        <select class="form-control" id="role" name="role" required>
                                            <option value="">Select Role</option>
                                            <option value="user" <?= old('role', $user['role']) == 'user' ? 'selected' : '' ?>>Field User</option>
                                            <option value="guest" <?= old('role', $user['role']) == 'guest' ? 'selected' : '' ?>>Guest</option>
                                        </select>
                                        <?php if (isset($errors['role'])): ?>
                                            <small class="text-danger"><?= $errors['role'] ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin" value="1"
                                                   <?= old('is_admin', $user['is_admin']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_admin">
                                                Admin Privileges
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="is_supervisor" name="is_supervisor" value="1"
                                                   <?= old('is_supervisor', $user['is_supervisor']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_supervisor">
                                                Supervisor Privileges
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="status">Status</label>
                                        <select class="form-control" id="status" name="status">
                                            <option value="1" <?= old('status', $user['status']) == '1' ? 'selected' : '' ?>>Active</option>
                                            <option value="0" <?= old('status', $user['status']) == '0' ? 'selected' : '' ?>>Inactive</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="id_photo">Profile Photo</label>
                                        <input type="file" class="form-control-file" id="id_photo" name="id_photo" accept="image/*">
                                        <small class="form-text text-muted">Upload a new photo to replace current one</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <?php if ($user['id_photo']): ?>
                                    <div class="form-group">
                                        <label>Current Photo</label><br>
                                        <img src="<?= imgcheck($user['id_photo']) ?>" alt="Current Photo" 
                                             class="img-thumbnail" width="100" height="100">
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Permissions & Districts -->
                <div class="col-md-4">
                    <!-- District Permissions -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">District Access</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label>Assigned Districts <span class="text-danger">*</span></label>
                                <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                    <?php 
                                    $userDistrictIds = array_column($userDistricts, 'district_id');
                                    foreach ($districts as $district): 
                                    ?>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input district-checkbox" 
                                               id="district_<?= $district['id'] ?>" 
                                               name="districts[]" 
                                               value="<?= $district['id'] ?>"
                                               <?= in_array($district['id'], old('districts') ?: $userDistrictIds) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="district_<?= $district['id'] ?>">
                                            <?= esc($district['name']) ?>
                                        </label>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php if (isset($errors['districts'])): ?>
                                    <small class="text-danger"><?= $errors['districts'] ?></small>
                                <?php endif; ?>
                            </div>

                            <div class="form-group">
                                <label for="default_district">Default District <span class="text-danger">*</span></label>
                                <select class="form-control" id="default_district" name="default_district" required>
                                    <option value="">Select Default District</option>
                                    <?php 
                                    $currentDefault = null;
                                    foreach ($userDistricts as $ud) {
                                        if ($ud['default_district']) {
                                            $currentDefault = $ud['district_id'];
                                            break;
                                        }
                                    }
                                    foreach ($districts as $district): 
                                    ?>
                                    <option value="<?= $district['id'] ?>" 
                                            <?= old('default_district', $currentDefault) == $district['id'] ? 'selected' : '' ?>>
                                        <?= esc($district['name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['default_district'])): ?>
                                    <small class="text-danger"><?= $errors['default_district'] ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- System Permissions -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">System Permissions</h3>
                        </div>
                        <div class="card-body">
                            <div style="max-height: 300px; overflow-y: auto;">
                                <?php 
                                $userPermissionIds = array_column($userPermissions, 'permission_id');
                                foreach ($permissions as $permission): 
                                ?>
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" 
                                           id="permission_<?= $permission['id'] ?>" 
                                           name="permissions[]" 
                                           value="<?= $permission['id'] ?>"
                                           <?= in_array($permission['id'], old('permissions') ?: $userPermissionIds) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="permission_<?= $permission['id'] ?>">
                                        <?= esc($permission['permission_text']) ?>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> Update User
                            </button>
                            <a href="<?= base_url('admin/users/' . $user['id']) ?>" class="btn btn-info">
                                <i class="fas fa-eye"></i> View Details
                            </a>
                            <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</section>

<script>
// Update default district dropdown when districts are selected/deselected
document.addEventListener('DOMContentLoaded', function() {
    const districtCheckboxes = document.querySelectorAll('.district-checkbox');
    const defaultDistrictSelect = document.getElementById('default_district');
    
    function updateDefaultDistrictOptions() {
        const selectedDistricts = Array.from(districtCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => ({
                value: cb.value,
                text: cb.nextElementSibling.textContent.trim()
            }));
        
        // Store current selection
        const currentValue = defaultDistrictSelect.value;
        
        // Clear current options except the first one
        defaultDistrictSelect.innerHTML = '<option value="">Select Default District</option>';
        
        // Add options for selected districts
        selectedDistricts.forEach(district => {
            const option = document.createElement('option');
            option.value = district.value;
            option.textContent = district.text;
            if (district.value === currentValue) {
                option.selected = true;
            }
            defaultDistrictSelect.appendChild(option);
        });
    }
    
    districtCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateDefaultDistrictOptions);
    });
    
    // Initialize on page load
    updateDefaultDistrictOptions();
});
</script>

<?= $this->endSection() ?>
