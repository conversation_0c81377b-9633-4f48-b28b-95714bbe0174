<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_header ?></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Admin Dashboard</a></li>
                    <li class="breadcrumb-item active">User Management</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        
        <!-- Action Buttons -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <a href="<?= base_url('admin/users/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add New User
                        </a>
                        <button type="button" class="btn btn-info" data-toggle="modal" data-target="#permissionsModal">
                            <i class="fas fa-key"></i> Manage Permissions
                        </button>
                        <a href="<?= base_url('admin/users/export') ?>" class="btn btn-secondary">
                            <i class="fas fa-download"></i> Export Users
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Organization Users</h3>
                        <div class="card-tools">
                            <div class="input-group input-group-sm" style="width: 250px;">
                                <input type="text" id="userSearch" class="form-control float-right" placeholder="Search users...">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-default">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap" id="usersTable">
                            <thead>
                                <tr>
                                    <th>Photo</th>
                                    <th>Name</th>
                                    <th>System No.</th>
                                    <th>Role</th>
                                    <th>Position</th>
                                    <th>Contact</th>
                                    <th>Districts</th>
                                    <th>Permissions</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <img src="<?= imgcheck($user['id_photo']) ?>" 
                                             alt="User Photo" 
                                             class="img-circle elevation-2" 
                                             width="40" height="40">
                                    </td>
                                    <td>
                                        <strong><?= esc($user['name']) ?></strong>
                                        <?php if ($user['is_admin']): ?>
                                            <span class="badge badge-danger">Admin</span>
                                        <?php endif; ?>
                                        <?php if ($user['is_supervisor']): ?>
                                            <span class="badge badge-warning">Supervisor</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><code><?= esc($user['sys_no']) ?></code></td>
                                    <td>
                                        <?php if ($user['role'] == 'user'): ?>
                                            <span class="badge badge-primary">Field User</span>
                                        <?php elseif ($user['role'] == 'guest'): ?>
                                            <span class="badge badge-secondary">Guest</span>
                                        <?php else: ?>
                                            <span class="badge badge-dark"><?= esc($user['role']) ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= esc($user['position']) ?: '<em>Not specified</em>' ?></td>
                                    <td>
                                        <?php if ($user['email']): ?>
                                            <div><i class="fas fa-envelope"></i> <?= esc($user['email']) ?></div>
                                        <?php endif; ?>
                                        <?php if ($user['phone']): ?>
                                            <div><i class="fas fa-phone"></i> <?= esc($user['phone']) ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            <?= $user['district_count'] ?> district<?= $user['district_count'] != 1 ? 's' : '' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-success">
                                            <?= $user['permission_count'] ?> permission<?= $user['permission_count'] != 1 ? 's' : '' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($user['status'] == 1): ?>
                                            <span class="badge badge-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge badge-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="<?= base_url('admin/users/' . $user['id']) ?>" 
                                               class="btn btn-sm btn-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('admin/users/' . $user['id'] . '/edit') ?>" 
                                               class="btn btn-sm btn-warning" title="Edit User">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($user['id'] != session()->get('emp_id')): ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-danger" 
                                                    title="Delete User"
                                                    onclick="confirmDelete(<?= $user['id'] ?>, '<?= esc($user['name']) ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete user <strong id="deleteUserName"></strong>?</p>
                <p class="text-danger">This action cannot be undone and will remove all user permissions and district assignments.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete User</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Permissions Management Modal -->
<div class="modal fade" id="permissionsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Manage System Permissions</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Permission management will be available in the next update.</p>
                <p>For now, permissions can be managed when creating or editing users.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(userId, userName) {
    document.getElementById('deleteUserName').textContent = userName;
    document.getElementById('deleteForm').action = '<?= base_url('admin/users/') ?>' + userId + '/delete';
    $('#deleteModal').modal('show');
}

// Search functionality
document.getElementById('userSearch').addEventListener('keyup', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('#usersTable tbody tr');
    
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});
</script>

<?= $this->endSection() ?>
