<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_header ?></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Admin Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('admin/users') ?>">User Management</a></li>
                    <li class="breadcrumb-item active">User Details</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        
        <!-- Action Buttons -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <a href="<?= base_url('admin/users/' . $user['id'] . '/edit') ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit User
                        </a>
                        <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Users
                        </a>
                        <?php if ($user['id'] != session()->get('emp_id')): ?>
                        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                            <i class="fas fa-trash"></i> Delete User
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- User Profile -->
            <div class="col-md-4">
                <div class="card card-primary card-outline">
                    <div class="card-body box-profile">
                        <div class="text-center">
                            <img class="profile-user-img img-fluid img-circle"
                                 src="<?= imgcheck($user['id_photo']) ?>"
                                 alt="User profile picture">
                        </div>

                        <h3 class="profile-username text-center"><?= esc($user['name']) ?></h3>

                        <p class="text-muted text-center">
                            <?= esc($user['position']) ?: 'No position specified' ?>
                        </p>

                        <ul class="list-group list-group-unbordered mb-3">
                            <li class="list-group-item">
                                <b>System Number</b> <span class="float-right"><code><?= esc($user['sys_no']) ?></code></span>
                            </li>
                            <li class="list-group-item">
                                <b>Role</b> 
                                <span class="float-right">
                                    <?php if ($user['role'] == 'user'): ?>
                                        <span class="badge badge-primary">Field User</span>
                                    <?php elseif ($user['role'] == 'guest'): ?>
                                        <span class="badge badge-secondary">Guest</span>
                                    <?php else: ?>
                                        <span class="badge badge-dark"><?= esc($user['role']) ?></span>
                                    <?php endif; ?>
                                </span>
                            </li>
                            <li class="list-group-item">
                                <b>Status</b> 
                                <span class="float-right">
                                    <?php if ($user['status'] == 1): ?>
                                        <span class="badge badge-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">Inactive</span>
                                    <?php endif; ?>
                                </span>
                            </li>
                            <li class="list-group-item">
                                <b>Privileges</b>
                                <div class="float-right">
                                    <?php if ($user['is_admin']): ?>
                                        <span class="badge badge-danger">Admin</span>
                                    <?php endif; ?>
                                    <?php if ($user['is_supervisor']): ?>
                                        <span class="badge badge-warning">Supervisor</span>
                                    <?php endif; ?>
                                    <?php if (!$user['is_admin'] && !$user['is_supervisor']): ?>
                                        <span class="text-muted">None</span>
                                    <?php endif; ?>
                                </div>
                            </li>
                        </ul>

                        <?php if ($user['email'] || $user['phone']): ?>
                        <strong><i class="fas fa-address-book mr-1"></i> Contact Information</strong>
                        <p class="text-muted">
                            <?php if ($user['email']): ?>
                                <i class="fas fa-envelope"></i> <?= esc($user['email']) ?><br>
                            <?php endif; ?>
                            <?php if ($user['phone']): ?>
                                <i class="fas fa-phone"></i> <?= esc($user['phone']) ?>
                            <?php endif; ?>
                        </p>
                        <?php endif; ?>

                        <strong><i class="fas fa-calendar mr-1"></i> Account Information</strong>
                        <p class="text-muted">
                            <small>Created: <?= date('M d, Y', strtotime($user['created_at'])) ?></small><br>
                            <small>Updated: <?= date('M d, Y', strtotime($user['updated_at'])) ?></small>
                        </p>
                    </div>
                </div>
            </div>

            <!-- User Details -->
            <div class="col-md-8">
                <!-- District Permissions -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-map-marker-alt"></i> District Access
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($userDistricts)): ?>
                            <p class="text-muted">No districts assigned to this user.</p>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($userDistricts as $district): ?>
                                <div class="col-md-6 mb-2">
                                    <div class="info-box">
                                        <span class="info-box-icon <?= $district['default_district'] ? 'bg-success' : 'bg-info' ?>">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text"><?= esc($district['district_name']) ?></span>
                                            <span class="info-box-number">
                                                <?php if ($district['default_district']): ?>
                                                    <small class="badge badge-success">Default</small>
                                                <?php endif; ?>
                                                <small class="text-muted"><?= esc($district['districtcode']) ?></small>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- System Permissions -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-key"></i> System Permissions
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($userPermissions)): ?>
                            <p class="text-muted">No specific permissions assigned to this user.</p>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($userPermissions as $permission): ?>
                                <div class="col-md-6 mb-2">
                                    <div class="callout callout-success">
                                        <h5><?= esc($permission['permission_text']) ?></h5>
                                        <p><small class="text-muted">Code: <?= esc($permission['permission_code']) ?></small></p>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Activity Log (Future Enhancement) -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history"></i> Recent Activity
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">Activity logging will be available in a future update.</p>
                        <div class="timeline">
                            <div class="time-label">
                                <span class="bg-green">Account Created</span>
                            </div>
                            <div>
                                <i class="fas fa-user bg-blue"></i>
                                <div class="timeline-item">
                                    <span class="time"><i class="fas fa-clock"></i> <?= date('M d, Y H:i', strtotime($user['created_at'])) ?></span>
                                    <h3 class="timeline-header">User account was created</h3>
                                    <div class="timeline-body">
                                        User account for <?= esc($user['name']) ?> was created in the system.
                                    </div>
                                </div>
                            </div>
                            <div>
                                <i class="fas fa-clock bg-gray"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete user <strong><?= esc($user['name']) ?></strong>?</p>
                <p class="text-danger">This action cannot be undone and will remove all user permissions and district assignments.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form action="<?= base_url('admin/users/' . $user['id'] . '/delete') ?>" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete User</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    $('#deleteModal').modal('show');
}
</script>

<?= $this->endSection() ?>
