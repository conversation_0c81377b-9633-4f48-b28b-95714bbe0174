<?= $this->extend('templates/adminlte/admindash') ?>
<?= $this->section('content') ?>

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success">Create New User</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>dashboard">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>users">Users</a></li>
                    <li class="breadcrumb-item active">Create User</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user-plus"></i> User Information
                        </h3>
                        <div class="card-tools">
                            <a href="<?= base_url() ?>users" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> Back to Users
                            </a>
                        </div>
                    </div>

                    <?= form_open('users/store', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                    <div class="card-body">
                        <!-- Display validation errors -->
                        <?php if (session()->getFlashdata('error')): ?>
                            <div class="alert alert-danger alert-dismissible">
                                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                                <h5><i class="icon fas fa-ban"></i> Error!</h5>
                                <?= session()->getFlashdata('error') ?>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <!-- Role Selection -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Role <span class="text-danger">*</span></label>
                                <select class="form-control" name="role" required>
                                    <option value="">Select Role</option>
                                    <?php foreach ($roles as $value => $label): ?>
                                        <option value="<?= $value ?>" <?= old('role') == $value ? 'selected' : '' ?>>
                                            <?= $label ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">Select the user's access level</div>
                            </div>

                            <!-- Status -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Status</label>
                                <select class="form-control" name="status">
                                    <option value="1" <?= old('status', '1') == '1' ? 'selected' : '' ?>>Active</option>
                                    <option value="0" <?= old('status') == '0' ? 'selected' : '' ?>>Inactive</option>
                                </select>
                                <div class="form-text">User account status</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Full Name -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" 
                                       placeholder="Enter full name" 
                                       value="<?= old('name') ?>" required>
                                <div class="form-text">User's complete name</div>
                            </div>

                            <!-- Password -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" name="password" 
                                       placeholder="Enter password" minlength="6" required>
                                <div class="form-text">Minimum 6 characters</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Position -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Position</label>
                                <input type="text" class="form-control" name="position" 
                                       placeholder="Job title or position" 
                                       value="<?= old('position') ?>">
                                <div class="form-text">Optional job title or position</div>
                            </div>

                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email Address</label>
                                <input type="email" class="form-control" name="email" 
                                       placeholder="<EMAIL>" 
                                       value="<?= old('email') ?>">
                                <div class="form-text">Optional email address</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Phone -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" name="phone" 
                                       placeholder="Phone number" 
                                       value="<?= old('phone') ?>">
                                <div class="form-text">Optional phone number</div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url() ?>users" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> Create User
                            </button>
                        </div>
                    </div>
                    <?= form_close() ?>
                </div>
            </div>

            <!-- Help Card -->
            <div class="col-md-4">
                <div class="card card-info">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle"></i> Help & Guidelines
                        </h3>
                    </div>
                    <div class="card-body">
                        <h6>Required Fields</h6>
                        <ul class="small text-muted">
                            <li>Full name (minimum 3 characters)</li>
                            <li>Password (minimum 6 characters)</li>
                            <li>Role selection</li>
                        </ul>

                        <h6>Optional Fields</h6>
                        <ul class="small text-muted">
                            <li>Position/title</li>
                            <li>Email address</li>
                            <li>Phone number</li>
                        </ul>

                        <h6>Role Descriptions</h6>
                        <ul class="small text-muted">
                            <li><strong>Administrator:</strong> Full system access</li>
                            <li><strong>Supervisor:</strong> Management level access</li>
                            <li><strong>User:</strong> Standard user access</li>
                            <li><strong>Guest:</strong> Limited read-only access</li>
                        </ul>

                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb"></i>
                            <strong>Tip:</strong> Provide contact information to help with account recovery and communication.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
$(document).ready(function() {
    // Form validation
    $('.needs-validation').on('submit', function(e) {
        if (this.checkValidity() === false) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });

    // Role-based styling
    $('select[name="role"]').on('change', function() {
        const role = $(this).val();
        const roleColors = {
            'admin': 'danger',
            'supervisor': 'warning', 
            'user': 'primary',
            'guest': 'secondary'
        };
        
        // Update select styling based on role
        $(this).removeClass('border-danger border-warning border-primary border-secondary')
               .addClass('border-' + (roleColors[role] || 'secondary'));
    });
});
</script>

<style>
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.text-danger {
    color: #dc3545 !important;
}

.card-tools .btn {
    margin-left: 0.25rem;
}

.alert {
    border-radius: 0.375rem;
}

.needs-validation .form-control:invalid {
    border-color: #dc3545;
}

.needs-validation .form-control:valid {
    border-color: #28a745;
}
</style>

<?= $this->endSection() ?>
