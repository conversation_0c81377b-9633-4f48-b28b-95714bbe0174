<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-plus"></i> Add New District
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations') ?>">Locations</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/provinces') ?>">Provinces</a></li>
                    <li class="breadcrumb-item active">Add District</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('dakoii/locations/provinces') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Provinces
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> Please fix the following errors:
            <ul class="mb-0 mt-2">
                <?php foreach ($errors as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Create Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus"></i> District Information
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open('dakoii/locations/districts/store', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">District Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?= old('name') ?>" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid district name.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="districtcode" class="form-label">District Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="districtcode" name="districtcode" 
                                           value="<?= old('districtcode') ?>" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid district code.
                                    </div>
                                    <small class="form-text text-muted">Unique identifier for the district</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="country_id" class="form-label">Country <span class="text-danger">*</span></label>
                                    <select class="form-select" id="country_id" name="country_id" required>
                                        <option value="">Select Country</option>
                                        <?php foreach ($countries as $country): ?>
                                            <option value="<?= $country['id'] ?>" 
                                                    <?= old('country_id') == $country['id'] ? 'selected' : '' ?>>
                                                <?= esc($country['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a country.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="province_id" class="form-label">Province <span class="text-danger">*</span></label>
                                    <select class="form-select" id="province_id" name="province_id" required>
                                        <option value="">Select Province</option>
                                        <?php foreach ($provinces as $province): ?>
                                            <option value="<?= $province['id'] ?>" 
                                                    <?= old('province_id', $selected_province_id ?? '') == $province['id'] ? 'selected' : '' ?>>
                                                <?= esc($province['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a province.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="json_id" class="form-label">JSON ID</label>
                                    <input type="text" class="form-control" id="json_id" name="json_id" 
                                           value="<?= old('json_id') ?>">
                                    <small class="form-text text-muted">Optional: JSON mapping identifier</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('dakoii/locations/provinces') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create District
                            </button>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <!-- Information Panel -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> District Guidelines
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Required Fields:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> District Name</li>
                        <li><i class="fas fa-check text-success"></i> District Code</li>
                        <li><i class="fas fa-check text-success"></i> Country</li>
                        <li><i class="fas fa-check text-success"></i> Province</li>
                    </ul>
                    
                    <h6 class="mt-3">Optional Fields:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-info text-info"></i> JSON ID (for mapping)</li>
                    </ul>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb text-warning"></i> Tips
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-arrow-right text-primary"></i> District names should be descriptive</li>
                        <li><i class="fas fa-arrow-right text-primary"></i> District codes must be unique</li>
                        <li><i class="fas fa-arrow-right text-primary"></i> Select the correct province</li>
                        <li><i class="fas fa-arrow-right text-primary"></i> JSON ID helps with data mapping</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Dynamic province filtering based on country selection
document.getElementById('country_id').addEventListener('change', function() {
    // You can add AJAX call here to filter provinces by country if needed
});
</script>

<?= $this->endSection() ?>
