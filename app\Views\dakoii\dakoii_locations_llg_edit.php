<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit"></i> Edit LLG
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations') ?>">Locations</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/provinces') ?>">Provinces</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/districts/' . $llg['province_id']) ?>">Districts</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/llgs/' . $llg['district_id']) ?>">LLGs</a></li>
                    <li class="breadcrumb-item active">Edit <?= esc($llg['name']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('dakoii/locations/llgs/' . $llg['district_id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to LLGs
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> Please fix the following errors:
            <ul class="mb-0 mt-2">
                <?php foreach ($errors as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Edit Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit"></i> Edit LLG Information
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open('dakoii/locations/llgs/' . $llg['id'] . '/update', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">LLG Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?= old('name', $llg['name']) ?>" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid LLG name.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="llgcode" class="form-label">LLG Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="llgcode" name="llgcode" 
                                           value="<?= old('llgcode', $llg['llgcode']) ?>" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid LLG code.
                                    </div>
                                    <small class="form-text text-muted">Unique identifier for the LLG</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="country_id" class="form-label">Country <span class="text-danger">*</span></label>
                                    <select class="form-select" id="country_id" name="country_id" required>
                                        <option value="">Select Country</option>
                                        <?php foreach ($countries as $country): ?>
                                            <option value="<?= $country['id'] ?>" 
                                                    <?= old('country_id', $llg['country_id']) == $country['id'] ? 'selected' : '' ?>>
                                                <?= esc($country['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a country.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="province_id" class="form-label">Province <span class="text-danger">*</span></label>
                                    <select class="form-select" id="province_id" name="province_id" required>
                                        <option value="">Select Province</option>
                                        <?php foreach ($provinces as $province): ?>
                                            <option value="<?= $province['id'] ?>" 
                                                    <?= old('province_id', $llg['province_id']) == $province['id'] ? 'selected' : '' ?>>
                                                <?= esc($province['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a province.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="district_id" class="form-label">District <span class="text-danger">*</span></label>
                                    <select class="form-select" id="district_id" name="district_id" required>
                                        <option value="">Select District</option>
                                        <?php if (isset($district)): ?>
                                            <option value="<?= $district['id'] ?>" 
                                                    <?= old('district_id', $llg['district_id']) == $district['id'] ? 'selected' : '' ?>>
                                                <?= esc($district['name']) ?>
                                            </option>
                                        <?php endif; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a district.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="json_id" class="form-label">JSON ID</label>
                                    <input type="text" class="form-control" id="json_id" name="json_id" 
                                           value="<?= old('json_id', $llg['json_id']) ?>">
                                    <small class="form-text text-muted">Optional: JSON mapping identifier</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('dakoii/locations/llgs/' . $llg['district_id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> Update LLG
                            </button>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <!-- Information Panel -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> LLG Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Current LLG:</strong><br>
                        <span class="text-primary"><?= esc($llg['name']) ?></span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>LLG Code:</strong><br>
                        <span class="badge bg-secondary"><?= esc($llg['llgcode']) ?></span>
                    </div>
                    
                    <?php if (!empty($llg['json_id'])): ?>
                    <div class="mb-3">
                        <strong>JSON ID:</strong><br>
                        <span class="text-muted"><?= esc($llg['json_id']) ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($district)): ?>
                    <div class="mb-3">
                        <strong>District:</strong><br>
                        <span class="text-info"><?= esc($district['name']) ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($province)): ?>
                    <div class="mb-3">
                        <strong>Province:</strong><br>
                        <span class="text-info"><?= esc($province['name']) ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <strong>Created:</strong><br>
                        <small class="text-muted">
                            <?= isset($llg['created_at']) ? date('M d, Y', strtotime($llg['created_at'])) : 'N/A' ?>
                        </small>
                    </div>
                    
                    <?php if (isset($llg['updated_at']) && $llg['updated_at']): ?>
                    <div class="mb-3">
                        <strong>Last Updated:</strong><br>
                        <small class="text-muted">
                            <?= date('M d, Y', strtotime($llg['updated_at'])) ?>
                        </small>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-warning"></i> Important Notes
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check text-success"></i> LLG name must be unique</li>
                        <li><i class="fas fa-check text-success"></i> LLG code must be unique</li>
                        <li><i class="fas fa-info text-info"></i> JSON ID is used for mapping purposes</li>
                        <li><i class="fas fa-warning text-warning"></i> Changes will affect all related wards</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?= $this->endSection() ?>
