<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit"></i> Edit Province
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations') ?>">Locations</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/provinces') ?>">Provinces</a></li>
                    <li class="breadcrumb-item active">Edit <?= esc($province['name']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('dakoii/locations/provinces') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Provinces
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> Please fix the following errors:
            <ul class="mb-0 mt-2">
                <?php foreach ($errors as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Edit Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit"></i> Edit Province Information
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open('dakoii/locations/provinces/' . $province['id'] . '/update', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Province Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?= old('name', $province['name']) ?>" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid province name.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="provincecode" class="form-label">Province Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="provincecode" name="provincecode" 
                                           value="<?= old('provincecode', $province['provincecode']) ?>" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid province code.
                                    </div>
                                    <small class="form-text text-muted">Unique identifier for the province</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="country_id" class="form-label">Country <span class="text-danger">*</span></label>
                                    <select class="form-select" id="country_id" name="country_id" required>
                                        <option value="">Select Country</option>
                                        <?php foreach ($countries as $country): ?>
                                            <option value="<?= $country['id'] ?>" 
                                                    <?= old('country_id', $province['country_id']) == $country['id'] ? 'selected' : '' ?>>
                                                <?= esc($country['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a country.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="json_id" class="form-label">JSON ID</label>
                                    <input type="text" class="form-control" id="json_id" name="json_id" 
                                           value="<?= old('json_id', $province['json_id']) ?>">
                                    <small class="form-text text-muted">Optional: JSON mapping identifier</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('dakoii/locations/provinces') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> Update Province
                            </button>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <!-- Information Panel -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> Province Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Current Province:</strong><br>
                        <span class="text-primary"><?= esc($province['name']) ?></span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Province Code:</strong><br>
                        <span class="badge bg-secondary"><?= esc($province['provincecode']) ?></span>
                    </div>
                    
                    <?php if (!empty($province['json_id'])): ?>
                    <div class="mb-3">
                        <strong>JSON ID:</strong><br>
                        <span class="text-muted"><?= esc($province['json_id']) ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <strong>Created:</strong><br>
                        <small class="text-muted">
                            <?= isset($province['created_at']) ? date('M d, Y', strtotime($province['created_at'])) : 'N/A' ?>
                        </small>
                    </div>
                    
                    <?php if (isset($province['updated_at']) && $province['updated_at']): ?>
                    <div class="mb-3">
                        <strong>Last Updated:</strong><br>
                        <small class="text-muted">
                            <?= date('M d, Y', strtotime($province['updated_at'])) ?>
                        </small>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-warning"></i> Important Notes
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check text-success"></i> Province name must be unique</li>
                        <li><i class="fas fa-check text-success"></i> Province code must be unique</li>
                        <li><i class="fas fa-info text-info"></i> JSON ID is used for mapping purposes</li>
                        <li><i class="fas fa-warning text-warning"></i> Changes will affect all related districts</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?= $this->endSection() ?>
