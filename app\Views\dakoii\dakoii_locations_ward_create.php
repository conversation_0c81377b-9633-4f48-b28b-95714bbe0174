<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-plus"></i> Add New Ward
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations') ?>">Locations</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/provinces') ?>">Provinces</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/districts/' . ($province['id'] ?? '')) ?>">Districts</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/llgs/' . ($district['id'] ?? '')) ?>">LLGs</a></li>
                    <li class="breadcrumb-item active">Add Ward</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('dakoii/locations/llgs/' . ($district['id'] ?? '')) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to LLGs
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> Please fix the following errors:
            <ul class="mb-0 mt-2">
                <?php foreach ($errors as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Create Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus"></i> Ward Information
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open('dakoii/locations/wards/store', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Ward Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?= old('name') ?>" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid ward name.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="wardcode" class="form-label">Ward Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="wardcode" name="wardcode" 
                                           value="<?= old('wardcode') ?>" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid ward code.
                                    </div>
                                    <small class="form-text text-muted">Unique identifier for the ward</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="country_id" class="form-label">Country <span class="text-danger">*</span></label>
                                    <select class="form-select" id="country_id" name="country_id" required>
                                        <option value="">Select Country</option>
                                        <?php foreach ($countries as $country): ?>
                                            <option value="<?= $country['id'] ?>" 
                                                    <?= old('country_id') == $country['id'] ? 'selected' : '' ?>>
                                                <?= esc($country['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a country.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="province_id" class="form-label">Province <span class="text-danger">*</span></label>
                                    <select class="form-select" id="province_id" name="province_id" required>
                                        <option value="">Select Province</option>
                                        <?php foreach ($provinces as $province): ?>
                                            <option value="<?= $province['id'] ?>" 
                                                    <?= old('province_id', $llg['province_id'] ?? '') == $province['id'] ? 'selected' : '' ?>>
                                                <?= esc($province['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a province.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="district_id" class="form-label">District <span class="text-danger">*</span></label>
                                    <select class="form-select" id="district_id" name="district_id" required>
                                        <option value="">Select District</option>
                                        <?php if (isset($district)): ?>
                                            <option value="<?= $district['id'] ?>" selected>
                                                <?= esc($district['name']) ?>
                                            </option>
                                        <?php endif; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a district.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="llg_id" class="form-label">LLG <span class="text-danger">*</span></label>
                                    <select class="form-select" id="llg_id" name="llg_id" required>
                                        <option value="">Select LLG</option>
                                        <?php if (isset($llg)): ?>
                                            <option value="<?= $llg['id'] ?>" selected>
                                                <?= esc($llg['name']) ?>
                                            </option>
                                        <?php endif; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select an LLG.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('dakoii/locations/llgs/' . ($district['id'] ?? '')) ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Ward
                            </button>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <!-- Information Panel -->
        <div class="col-lg-4">
            <?php if (isset($llg)): ?>
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-landmark"></i> Selected LLG
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>LLG:</strong><br>
                        <span class="text-primary"><?= esc($llg['name']) ?></span>
                    </div>
                    <div class="mb-2">
                        <strong>Code:</strong><br>
                        <span class="badge bg-secondary"><?= esc($llg['llgcode']) ?></span>
                    </div>
                    <?php if (isset($district)): ?>
                    <div class="mb-2">
                        <strong>District:</strong><br>
                        <span class="text-info"><?= esc($district['name']) ?></span>
                    </div>
                    <?php endif; ?>
                    <?php if (isset($province)): ?>
                    <div class="mb-2">
                        <strong>Province:</strong><br>
                        <span class="text-info"><?= esc($province['name']) ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> Ward Guidelines
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Required Fields:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> Ward Name</li>
                        <li><i class="fas fa-check text-success"></i> Ward Code</li>
                        <li><i class="fas fa-check text-success"></i> Country</li>
                        <li><i class="fas fa-check text-success"></i> Province</li>
                        <li><i class="fas fa-check text-success"></i> District</li>
                        <li><i class="fas fa-check text-success"></i> LLG</li>
                    </ul>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb text-warning"></i> Tips
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-arrow-right text-primary"></i> Ward names should be descriptive</li>
                        <li><i class="fas fa-arrow-right text-primary"></i> Ward codes must be unique</li>
                        <li><i class="fas fa-arrow-right text-primary"></i> Select the correct LLG</li>
                        <li><i class="fas fa-arrow-right text-primary"></i> Wards are the smallest administrative units</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?= $this->endSection() ?>
