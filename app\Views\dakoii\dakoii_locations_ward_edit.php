<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit"></i> Edit Ward
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations') ?>">Locations</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/provinces') ?>">Provinces</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/districts/' . $ward['province_id']) ?>">Districts</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/llgs/' . $ward['district_id']) ?>">LLGs</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/wards/' . $ward['llg_id']) ?>">Wards</a></li>
                    <li class="breadcrumb-item active">Edit <?= esc($ward['name']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('dakoii/locations/wards/' . $ward['llg_id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Wards
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> Please fix the following errors:
            <ul class="mb-0 mt-2">
                <?php foreach ($errors as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Edit Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit"></i> Edit Ward Information
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open('dakoii/locations/wards/' . $ward['id'] . '/update', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Ward Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?= old('name', $ward['name']) ?>" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid ward name.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="wardcode" class="form-label">Ward Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="wardcode" name="wardcode" 
                                           value="<?= old('wardcode', $ward['wardcode']) ?>" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid ward code.
                                    </div>
                                    <small class="form-text text-muted">Unique identifier for the ward</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="country_id" class="form-label">Country <span class="text-danger">*</span></label>
                                    <select class="form-select" id="country_id" name="country_id" required>
                                        <option value="">Select Country</option>
                                        <?php foreach ($countries as $country): ?>
                                            <option value="<?= $country['id'] ?>" 
                                                    <?= old('country_id', $ward['country_id']) == $country['id'] ? 'selected' : '' ?>>
                                                <?= esc($country['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a country.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="province_id" class="form-label">Province <span class="text-danger">*</span></label>
                                    <select class="form-select" id="province_id" name="province_id" required>
                                        <option value="">Select Province</option>
                                        <?php foreach ($provinces as $province): ?>
                                            <option value="<?= $province['id'] ?>" 
                                                    <?= old('province_id', $ward['province_id']) == $province['id'] ? 'selected' : '' ?>>
                                                <?= esc($province['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a province.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="district_id" class="form-label">District <span class="text-danger">*</span></label>
                                    <select class="form-select" id="district_id" name="district_id" required>
                                        <option value="">Select District</option>
                                        <?php if (isset($district)): ?>
                                            <option value="<?= $district['id'] ?>" 
                                                    <?= old('district_id', $ward['district_id']) == $district['id'] ? 'selected' : '' ?>>
                                                <?= esc($district['name']) ?>
                                            </option>
                                        <?php endif; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a district.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="llg_id" class="form-label">LLG <span class="text-danger">*</span></label>
                                    <select class="form-select" id="llg_id" name="llg_id" required>
                                        <option value="">Select LLG</option>
                                        <?php if (isset($llg)): ?>
                                            <option value="<?= $llg['id'] ?>" 
                                                    <?= old('llg_id', $ward['llg_id']) == $llg['id'] ? 'selected' : '' ?>>
                                                <?= esc($llg['name']) ?>
                                            </option>
                                        <?php endif; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select an LLG.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('dakoii/locations/wards/' . $ward['llg_id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> Update Ward
                            </button>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <!-- Information Panel -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> Ward Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Current Ward:</strong><br>
                        <span class="text-primary"><?= esc($ward['name']) ?></span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Ward Code:</strong><br>
                        <span class="badge bg-secondary"><?= esc($ward['wardcode']) ?></span>
                    </div>
                    
                    <?php if (isset($llg)): ?>
                    <div class="mb-3">
                        <strong>LLG:</strong><br>
                        <span class="text-info"><?= esc($llg['name']) ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($district)): ?>
                    <div class="mb-3">
                        <strong>District:</strong><br>
                        <span class="text-info"><?= esc($district['name']) ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($province)): ?>
                    <div class="mb-3">
                        <strong>Province:</strong><br>
                        <span class="text-info"><?= esc($province['name']) ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <strong>Created:</strong><br>
                        <small class="text-muted">
                            <?= isset($ward['created_at']) ? date('M d, Y', strtotime($ward['created_at'])) : 'N/A' ?>
                        </small>
                    </div>
                    
                    <?php if (isset($ward['updated_at']) && $ward['updated_at']): ?>
                    <div class="mb-3">
                        <strong>Last Updated:</strong><br>
                        <small class="text-muted">
                            <?= date('M d, Y', strtotime($ward['updated_at'])) ?>
                        </small>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-warning"></i> Important Notes
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check text-success"></i> Ward name must be unique</li>
                        <li><i class="fas fa-check text-success"></i> Ward code must be unique</li>
                        <li><i class="fas fa-info text-info"></i> Wards are the smallest administrative units</li>
                        <li><i class="fas fa-warning text-warning"></i> Changes will affect all related data</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?= $this->endSection() ?>
