<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organizations') ?>">Organizations</a></li>
        <li class="breadcrumb-item active"><?= esc($org['name']) ?></li>
    </ol>
</nav>

<!-- Organization Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0"><?= esc($org['name']) ?></h2>
        <p class="text-muted mb-0">Organization Code: <code><?= esc($org['orgcode']) ?></code></p>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
        <a href="<?= base_url('dakoii/organizations/edit/' . $org['id']) ?>" class="btn btn-primary">
            <i class="fas fa-edit"></i> Edit Organization
        </a>
        <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
            <i class="fas fa-trash"></i> Delete Organization
        </button>
    </div>
</div>

<div class="row">
    <!-- Organization Details -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Organization Details
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Logo Section -->
                    <div class="col-md-4 text-center mb-4">
                        <?php if (!empty($org['orglogo'])): ?>
                            <img src="<?= imgcheck($org['orglogo']) ?>" alt="Organization Logo" 
                                 class="img-fluid rounded shadow" style="max-height: 200px;">
                        <?php else: ?>
                            <div class="bg-light rounded d-flex align-items-center justify-content-center shadow" 
                                 style="height: 200px;">
                                <i class="fas fa-building fa-4x text-muted"></i>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Details Section -->
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-medium text-muted" style="width: 40%;">Organization Name:</td>
                                <td><?= esc($org['name']) ?></td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted">Organization Code:</td>
                                <td><code class="bg-light px-2 py-1 rounded"><?= esc($org['orgcode']) ?></code></td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted">Description:</td>
                                <td><?= nl2br(esc($org['description'])) ?: '<em class="text-muted">No description provided</em>' ?></td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted">Status:</td>
                                <td>
                                    <span class="badge bg-<?= $org['is_active'] ? 'success' : 'danger' ?> fs-6">
                                        <i class="fas fa-<?= $org['is_active'] ? 'check' : 'times' ?>"></i>
                                        <?= $org['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted">License Status:</td>
                                <td>
                                    <span class="badge bg-<?= $org['license_status'] == 'paid' ? 'success' : 'warning' ?> fs-6">
                                        <i class="fas fa-<?= $org['license_status'] == 'paid' ? 'crown' : 'clock' ?>"></i>
                                        <?= ucfirst($org['license_status']) ?>
                                    </span>
                                    <button type="button" class="btn btn-sm btn-outline-primary ms-2" 
                                            data-bs-toggle="modal" data-bs-target="#licenseModal">
                                        <i class="fas fa-key"></i> Update License
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted">Location Lock:</td>
                                <td>
                                    <?php if ($org['is_locationlocked']): ?>
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-lock"></i> Locked
                                        </span>
                                        <div class="mt-1">
                                            <small class="text-muted">
                                                <?php if (isset($country_name)): ?>
                                                    Country: <?= esc($country_name) ?><br>
                                                <?php endif; ?>
                                                <?php if (isset($province_name)): ?>
                                                    Province: <?= esc($province_name) ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php else: ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-unlock"></i> Unlocked
                                        </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>

                        <!-- Dynamic User Statistics -->
                        <div class="mt-4">
                            <h6 class="text-muted mb-3">User Statistics</h6>
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h5 mb-0 text-primary"><?= count($admins) ?></div>
                                        <small class="text-muted">Admins</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h5 mb-0 text-success"><?= $total_active_users ?></div>
                                        <small class="text-muted">Active Users</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border rounded p-2">
                                        <div class="h5 mb-0 text-info"><?= $total_users ?></div>
                                        <small class="text-muted">Total Users</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Administrators -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-user-shield"></i> System Administrators
                    <span class="badge bg-primary"><?= count($admins) ?></span>
                </h5>
                <div class="btn-group">
                    <a href="<?= base_url('dakoii/system-admins') ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-list"></i> View All
                    </a>
                    <a href="<?= base_url('dakoii/system-admins/create') ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> Add Admin
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($admins)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($admins as $admin): ?>
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?= esc($admin['name']) ?></h6>
                                    <p class="mb-1 text-muted"><?= !empty($admin['email']) ? esc($admin['email']) : 'No email provided' ?></p>
                                    <div>
                                        <span class="badge bg-info me-1"><?= ucfirst(esc($admin['role'])) ?></span>
                                        <span class="badge bg-<?= $admin['status'] ? 'success' : 'danger' ?>">
                                            <?= $admin['status'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                            data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="<?= base_url('dakoii/system-admins/show/' . $admin['id']) ?>">
                                                <i class="fas fa-eye"></i> View Details
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="<?= base_url('dakoii/system-admins/edit/' . $admin['id']) ?>">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No administrators found</p>
                        <a href="<?= base_url('dakoii/system-admins/create') ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add First Admin
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- License Status Modal -->
<div class="modal fade" id="licenseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key"></i> Update License Status
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('dakoii/organizations/update-license/' . $org['id']) ?>" method="post">
            <?= csrf_field() ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">License Status</label>
                    <select class="form-select" name="license_status" required>
                        <option value="paid" <?= $org['license_status'] == 'paid' ? 'selected' : '' ?>>
                            <i class="fas fa-crown"></i> Paid License
                        </option>
                        <option value="trial" <?= $org['license_status'] == 'trial' ? 'selected' : '' ?>>
                            <i class="fas fa-clock"></i> Trial License
                        </option>
                    </select>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Note:</strong> Changing the license status will affect the organization's access to premium features.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update License
                </button>
            </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle"></i> Confirm Delete
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('dakoii/organizations/delete/' . $org['id']) ?>" method="post">
            <?= csrf_field() ?>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This action cannot be undone!
                </div>
                <p>Are you sure you want to delete the organization <strong>"<?= esc($org['name']) ?>"</strong>?</p>
                <p class="text-muted">This will permanently remove:</p>
                <ul class="text-muted">
                    <li>Organization information</li>
                    <li>Organization logo</li>
                    <li>All associated data</li>
                </ul>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                    <label class="form-check-label" for="confirmDelete">
                        I understand that this action cannot be undone
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                    <i class="fas fa-trash"></i> Delete Organization
                </button>
            </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enable/disable delete button based on checkbox
    const confirmCheckbox = document.getElementById('confirmDelete');
    const deleteBtn = document.getElementById('deleteBtn');

    confirmCheckbox.addEventListener('change', function() {
        deleteBtn.disabled = !this.checked;
    });

    // Reset checkbox when modal is hidden
    const deleteModal = document.getElementById('deleteModal');
    deleteModal.addEventListener('hidden.bs.modal', function() {
        confirmCheckbox.checked = false;
        deleteBtn.disabled = true;
    });
});
</script>

<?= $this->endSection() ?>
