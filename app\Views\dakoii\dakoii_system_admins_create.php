<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/system-admins') ?>">System Administrators</a></li>
        <li class="breadcrumb-item active">Create Administrator</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Create System Administrator</h2>
        <p class="text-muted mb-0">Add a new organization administrator to the system</p>
    </div>
    <a href="<?= base_url('dakoii/system-admins') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left"></i> Back to List
    </a>
</div>

<!-- Flash Messages -->
<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Create Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus"></i> Administrator Information
                </h5>
            </div>
            <div class="card-body">
                <form action="<?= base_url('dakoii/system-admins/store') ?>" method="post">
                <?= csrf_field() ?>
                
                <div class="row">
                    <!-- Organization Selection -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Organization <span class="text-danger">*</span></label>
                        <select class="form-select" name="orgcode">
                            <option value="">Select Organization</option>
                            <?php foreach ($organizations as $org): ?>
                                <option value="<?= esc($org['orgcode']) ?>" 
                                        <?= old('orgcode') == $org['orgcode'] ? 'selected' : '' ?>>
                                    <?= esc($org['name']) ?> (<?= esc($org['orgcode']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Position -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Position</label>
                        <input type="text" class="form-control" name="position"
                               placeholder="Job title or position" value="<?= old('position') ?>">
                        <div class="form-text">Optional job title or position</div>
                    </div>
                </div>

                <div class="row">
                    <!-- Full Name -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="name"
                               placeholder="Enter full name" value="<?= old('name') ?>">
                    </div>

                    <!-- Email -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" name="email" required
                               placeholder="Enter email address" value="<?= old('email') ?>">
                        <div class="form-text">Must be unique across the system</div>
                    </div>
                </div>

                <div class="row">
                    <!-- Password -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Password <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" name="password"
                               placeholder="Enter password">
                        <div class="form-text">Minimum 6 characters</div>
                    </div>

                    <!-- Phone -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Phone Number</label>
                        <input type="text" class="form-control" name="phone"
                               placeholder="Enter phone number" value="<?= old('phone') ?>">
                        <div class="form-text">Optional contact number</div>
                    </div>
                </div>

                <!-- Status -->
                <div class="mb-3">
                    <label class="form-label">Account Status</label>
                    <select class="form-select" name="status">
                        <option value="1" <?= old('status', '1') == '1' ? 'selected' : '' ?>>Active</option>
                        <option value="0" <?= old('status') == '0' ? 'selected' : '' ?>>Inactive</option>
                    </select>
                    <div class="form-text">Set initial account status</div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="<?= base_url('dakoii/system-admins') ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Administrator
                    </button>
                </div>

                </form>
            </div>
        </div>
    </div>

    <!-- Help Panel -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Information
                </h6>
            </div>
            <div class="card-body">
                <h6>System Administrator Role</h6>
                <p class="small text-muted">
                    System administrators are organization-level users with admin privileges. 
                    They can manage their organization's data and users.
                </p>

                <h6>Required Fields</h6>
                <ul class="small text-muted">
                    <li>Organization selection</li>
                    <li>Full name</li>
                    <li>Unique email address</li>
                    <li>Secure password (min 6 chars)</li>
                </ul>

                <h6>Optional Fields</h6>
                <ul class="small text-muted">
                    <li>Position/title</li>
                    <li>Phone number</li>
                </ul>

                <div class="alert alert-info">
                    <i class="fas fa-lightbulb"></i>
                    <strong>Tip:</strong> Provide contact information to help with account recovery and communication.
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
