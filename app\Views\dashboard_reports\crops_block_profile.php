<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_header ?></h1>
            </div>
            <div class="col-sm-6">
                <div class="float-right">
                    <a href="<?= base_url('staff/crops-farm-blocks') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">
        <!-- Summary Cards Row -->
        <div class="row">
            <!-- Total Plants Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?= number_format($block['total_plants'] ?? 0) ?></h3>
                        <p>Total Plants</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                </div>
            </div>
            <!-- Total Area Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3><?= number_format($block['total_area'] ?? 0, 2) ?></h3>
                        <p>Total Area (Ha)</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-ruler-combined"></i>
                    </div>
                </div>
            </div>
            <!-- Affected Plants Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3><?= number_format($totals['diseases']['affected_plants'] ?? 0) ?></h3>
                        <p>Disease Affected Plants</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-biohazard"></i>
                    </div>
                </div>
            </div>
            <!-- Total Harvests Card -->
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3><?= number_format($totals['harvests'] ?? 0) ?></h3>
                        <p>Total Harvests</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-tractor"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Block Information -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Block Information</h3>
                    </div>
                    <div class="card-body">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Block Code</th>
                                <td><?= $block['block_code'] ?></td>
                            </tr>
                            <tr>
                                <th>Crop Type</th>
                                <td><?= $block['crop_name'] ?></td>
                            </tr>
                            <tr>
                                <th>Block Site</th>
                                <td><?= $block['block_site'] ?></td>
                            </tr>
                            <tr>
                                <th>Province</th>
                                <td><?= $block['province_name'] ?></td>
                            </tr>
                            <tr>
                                <th>District</th>
                                <td><?= $block['district_name'] ?></td>
                            </tr>
                            <tr>
                                <th>LLG</th>
                                <td><?= $block['llg_name'] ?></td>
                            </tr>
                            <tr>
                                <th>Ward</th>
                                <td><?= $block['ward_name'] ?></td>
                            </tr>
                            <tr>
                                <th>Village</th>
                                <td><?= $block['village'] ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Farmer Information -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Farmer Information</h3>
                    </div>
                    <div class="card-body">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Name</th>
                                <td><?= trim($block['given_name'] . ' ' . $block['surname']) ?></td>
                            </tr>
                            <tr>
                                <th>Gender</th>
                                <td><?= ucfirst($block['gender']) ?></td>
                            </tr>
                            <tr>
                                <th>Date of Birth</th>
                                <td><?= date('d/m/Y', strtotime($block['date_of_birth'])) ?></td>
                            </tr>
                            <tr>
                                <th>Phone</th>
                                <td><?= $block['phone'] ?? 'N/A' ?></td>
                            </tr>
                            <tr>
                                <th>Email</th>
                                <td><?= $block['email'] ?? 'N/A' ?></td>
                            </tr>
                            <tr>
                                <th>Village</th>
                                <td><?= $block['farmer_village'] ?? 'N/A' ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Map -->
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Block Location</h3>
                    </div>
                    <div class="card-body">
                        <div id="map" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Disease Data -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Disease Data</h3>
                    </div>
                    <div class="card-body table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Disease</th>
                                    <th>Affected Plants</th>
                                    <th>Area (Ha)</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($diseases as $disease): ?>
                                <tr>
                                    <td><?= $disease['infection_name'] ?? $disease['disease_name'] ?></td>
                                    <td><?= number_format($disease['number_of_plants']) ?></td>
                                    <td><?= number_format($disease['hectares'], 2) ?></td>
                                    <td><?= date('d/m/Y', strtotime($disease['action_date'])) ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php if (empty($diseases)): ?>
                                <tr>
                                    <td colspan="4" class="text-center">No disease data available</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Fertilizer Usage -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Fertilizer Usage</h3>
                    </div>
                    <div class="card-body table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Fertilizer</th>
                                    <th>Brand</th>
                                    <th>Quantity</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($fertilizers as $fertilizer): ?>
                                <tr>
                                    <td><?= $fertilizer['fertilizer_name'] ?? $fertilizer['name'] ?></td>
                                    <td><?= $fertilizer['brand'] ?></td>
                                    <td><?= number_format($fertilizer['quantity']) . ' ' . $fertilizer['unit_of_measure'] ?></td>
                                    <td><?= date('d/m/Y', strtotime($fertilizer['action_date'])) ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php if (empty($fertilizers)): ?>
                                <tr>
                                    <td colspan="4" class="text-center">No fertilizer data available</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Pesticide Usage -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Pesticide Usage</h3>
                    </div>
                    <div class="card-body table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Pesticide</th>
                                    <th>Brand</th>
                                    <th>Quantity</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pesticides as $pesticide): ?>
                                <tr>
                                    <td><?= $pesticide['pesticide_name'] ?? $pesticide['name'] ?></td>
                                    <td><?= $pesticide['brand'] ?></td>
                                    <td><?= number_format($pesticide['quantity']) . ' ' . $pesticide['unit_of_measure'] ?></td>
                                    <td><?= date('d/m/Y', strtotime($pesticide['action_date'])) ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php if (empty($pesticides)): ?>
                                <tr>
                                    <td colspan="4" class="text-center">No pesticide data available</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Harvest Data -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Harvest Data</h3>
                    </div>
                    <div class="card-body table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Quantity</th>
                                    <th>Unit</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($harvests as $harvest): ?>
                                <tr>
                                    <td><?= $harvest['item'] ?></td>
                                    <td><?= number_format($harvest['quantity']) ?></td>
                                    <td><?= $harvest['unit_of_measure'] ?></td>
                                    <td><?= date('d/m/Y', strtotime($harvest['harvest_date'])) ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php if (empty($harvests)): ?>
                                <tr>
                                    <td colspan="4" class="text-center">No harvest data available</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Leaflet CSS and JS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize map
    var map = L.map('map').setView([<?= $block['lat'] ?>, <?= $block['lon'] ?>], 13);
    
    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    
    // Add marker for block location
    L.marker([<?= $block['lat'] ?>, <?= $block['lon'] ?>])
        .addTo(map)
        .bindPopup('<?= $block['block_code'] ?><br><?= $block['block_site'] ?>');
});
</script>

<?= $this->endSection() ?> 