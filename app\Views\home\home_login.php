<?= $this->extend("templates/nolstemp"); ?>
<?= $this->section('content'); ?>

<div class="container py-5">
    <div class="row justify-content-center">
        <!-- Admin Login -->
        <div class="col-md-5 mb-4">
            <div class="card shadow-lg border-0 rounded-lg">
                <div class="card-header bg-gradient-primary text-white text-center py-4" 
                     style="background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);">
                    <i class="fas fa-user-shield fa-2x mb-2"></i>
                    <h3 class="font-weight-light mb-0">Staff Login</h3>
                    <p class="small mb-0">System access</p>
                </div>

                <div class="card-body p-4">
                    <?php if (session()->has('error')) : ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle mr-2"></i> <?= session('error') ?>
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    <?php endif; ?>

                    <?= form_open('login', ['class' => 'needs-validation', 'novalidate' => '']) ?>
                        <div class="form-group">
                            <label class="small mb-1" for="admin_identifier">
                                <i class="fas fa-envelope mr-1"></i> Email / <i class="fas fa-id-card mr-1"></i> ID
                            </label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                </div>
                                <input type="text" class="form-control py-4" id="admin_identifier"
                                       name="identifier" placeholder="Enter your Email address or ID" required>
                            </div>
                            <small class="form-text text-muted">You can use either your Email address or ID to login</small>
                        </div>

                        <div class="form-group">
                            <label class="small mb-1" for="admin_password">Password</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                </div>
                                <input type="password" class="form-control py-4" id="admin_password" 
                                       name="password" placeholder="Password" required>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success btn-block py-2">
                            <i class="fas fa-sign-in-alt mr-2"></i> Login
                        </button>
                    <?= form_close() ?>
                </div>
            </div>
        </div>

        
    </div>

    <!-- Help Section -->
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card bg-light border-0">
                <div class="card-body text-center">
                    <div class="row">
                        <div class="col-md-6 border-right">
                            <h5><i class="fas fa-question-circle text-success mr-2"></i>Need Admin Help?</h5>
                            <p class="small mb-0">Contact system administrator</p>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-headset text-success mr-2"></i>Staff Support</h5>
                            <p class="small mb-0">Contact your supervisor</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
}

.input-group-text {
    background-color: #f8f9fa;
    border-right: none;
}

.form-control {
    border-left: none;
}

.form-control:focus {
    box-shadow: none;
    border-color: #ced4da;
}

.btn-success {
    transition: all 0.3s ease;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(46, 125, 50, 0.2);
}

.border-right {
    border-right: 1px solid #dee2e6;
}

@media (max-width: 768px) {
    .border-right {
        border-right: none;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
    }
}
</style>

<?= $this->endSection() ?>