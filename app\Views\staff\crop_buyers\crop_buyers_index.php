<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><?= esc($page_header) ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item active">Crop Buyers</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="<?= base_url('staff/crop-buyers/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Crop Buyer
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Crop Buyers List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Crop Buyers List</h5>
                        <span class="badge bg-primary"><?= count($buyers) ?> Total</span>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($buyers)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-handshake fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No crop buyers found</h5>
                            <p class="text-muted">Start by adding your first crop buyer.</p>
                            <a href="<?= base_url('staff/crop-buyers/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add First Crop Buyer
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="cropBuyersTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Buyer Code</th>
                                        <th>Name</th>
                                        <th>Crop</th>
                                        <th>Contact</th>
                                        <th>Operation Span</th>
                                        <th>Address</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($buyers as $buyer): ?>
                                        <tr>
                                            <td>
                                                <strong><?= esc($buyer['buyer_code']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($buyer['name']) ?></strong>
                                                </div>
                                                <?php if (!empty($buyer['email'])): ?>
                                                    <small class="text-muted"><?= esc($buyer['email']) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <?= esc($buyer['crop_name'] ?? 'N/A') ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (!empty($buyer['contact_number'])): ?>
                                                    <i class="fas fa-phone text-primary"></i>
                                                    <?= esc($buyer['contact_number']) ?>
                                                <?php else: ?>
                                                    <span class="text-muted">No contact</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $buyer['operation_span'] == 'national' ? 'info' : 'warning' ?>">
                                                    <?= ucfirst(esc($buyer['operation_span'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?= !empty($buyer['address']) ? esc($buyer['address']) : '<span class="text-muted">No address</span>' ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $buyer['status'] == 'active' ? 'success' : 'secondary' ?>">
                                                    <?= ucfirst(esc($buyer['status'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('staff/crop-buyers/' . $buyer['id']) ?>"
                                                       class="btn btn-sm btn-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('staff/crop-buyers/' . $buyer['id'] . '/edit') ?>"
                                                       class="btn btn-sm btn-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>



<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#cropBuyersTable').DataTable({
        responsive: true,
        order: [[0, 'desc']],
        pageLength: 25,
        language: {
            search: "Search crop buyers:",
            lengthMenu: "Show _MENU_ crop buyers per page",
            info: "Showing _START_ to _END_ of _TOTAL_ crop buyers",
            infoEmpty: "No crop buyers found",
            infoFiltered: "(filtered from _MAX_ total crop buyers)"
        }
    });
});
</script>
<?= $this->endSection() ?>
