<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><?= esc($page_header) ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/crop-buyers') ?>">Crop Buyers</a></li>
                            <li class="breadcrumb-item active">Details</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff/crop-buyers/' . $buyer['id'] . '/edit') ?>" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Buyer
                    </a>
                    <a href="<?= base_url('staff/crop-buyers') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Buyer Details -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Crop Buyer Details</h5>
                        <span class="badge bg-<?= $buyer['status'] == 'active' ? 'success' : 'secondary' ?> fs-6">
                            <?= ucfirst(esc($buyer['status'])) ?>
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Buyer Information -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Basic Information</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold text-muted">Buyer Code:</td>
                                            <td><span class="badge bg-dark"><?= esc($buyer['buyer_code']) ?></span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Name:</td>
                                            <td><strong><?= esc($buyer['name']) ?></strong></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Crop:</td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <?= esc($buyer['crop_name'] ?? 'N/A') ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Operation Span:</td>
                                            <td>
                                                <span class="badge bg-<?= $buyer['operation_span'] == 'national' ? 'info' : 'warning' ?>">
                                                    <?= ucfirst(esc($buyer['operation_span'])) ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Status:</td>
                                            <td>
                                                <span class="badge bg-<?= $buyer['status'] == 'active' ? 'success' : 'secondary' ?>">
                                                    <?= ucfirst(esc($buyer['status'])) ?>
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-phone me-2"></i>Contact Information</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold text-muted">Phone:</td>
                                            <td>
                                                <?php if (!empty($buyer['contact_number'])): ?>
                                                    <i class="fas fa-phone text-primary me-2"></i>
                                                    <a href="tel:<?= esc($buyer['contact_number']) ?>" class="text-decoration-none">
                                                        <?= esc($buyer['contact_number']) ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">No contact number</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Email:</td>
                                            <td>
                                                <?php if (!empty($buyer['email'])): ?>
                                                    <i class="fas fa-envelope text-primary me-2"></i>
                                                    <a href="mailto:<?= esc($buyer['email']) ?>" class="text-decoration-none">
                                                        <?= esc($buyer['email']) ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">No email address</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Address:</td>
                                            <td>
                                                <?php if (!empty($buyer['address'])): ?>
                                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                                    <?= nl2br(esc($buyer['address'])) ?>
                                                <?php else: ?>
                                                    <span class="text-muted">No address provided</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <?php if (!empty($buyer['description'])): ?>
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Description</h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-0"><?= nl2br(esc($buyer['description'])) ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Timestamps -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Record Information</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td class="fw-bold text-muted">Created:</td>
                                                    <td>
                                                        <?php if (!empty($buyer['created_at'])): ?>
                                                            <?= date('M d, Y \a\t g:i A', strtotime($buyer['created_at'])) ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">N/A</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold text-muted">Created By:</td>
                                                    <td>
                                                        <?= !empty($buyer['created_by']) ? 'User ID: ' . esc($buyer['created_by']) : '<span class="text-muted">N/A</span>' ?>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td class="fw-bold text-muted">Last Updated:</td>
                                                    <td>
                                                        <?php if (!empty($buyer['updated_at'])): ?>
                                                            <?= date('M d, Y \a\t g:i A', strtotime($buyer['updated_at'])) ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">Never updated</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold text-muted">Updated By:</td>
                                                    <td>
                                                        <?= !empty($buyer['updated_by']) ? 'User ID: ' . esc($buyer['updated_by']) : '<span class="text-muted">N/A</span>' ?>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a href="<?= base_url('staff/crop-buyers') ?>" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to List
                                    </a>
                                </div>
                                <div>
                                    <a href="<?= base_url('staff/crop-buyers/' . $buyer['id'] . '/edit') ?>" class="btn btn-primary">
                                        <i class="fas fa-edit"></i> Edit Buyer
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// No additional scripts needed
</script>
<?= $this->endSection() ?>
