<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3">
        <a href="<?= base_url('staff/crops-farm-blocks') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Crops Farm Blocks
        </a>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-plus"></i> <?= esc($page_header) ?>
                </h6>
            </div>
            <div class="card-body">
                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger">
                        <?= session()->getFlashdata('error') ?>
                    </div>
                <?php endif; ?>

                <?= form_open('staff/crops-farm-blocks', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                    <?= csrf_field() ?>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="farmer_id" class="form-label">Farmer <span class="text-danger">*</span></label>
                                <select class="form-select select2-farmer" id="farmer_id" name="farmer_id" required>
                                    <option value="">Select Farmer</option>
                                    <?php foreach ($farmers as $farmer): ?>
                                        <option value="<?= $farmer['id'] ?>">
                                            <?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?> (<?= esc($farmer['farmer_code']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Please select a farmer.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="crop_id" class="form-label">Crop <span class="text-danger">*</span></label>
                                <select class="form-select" id="crop_id" name="crop_id" required>
                                    <option value="">Select Crop</option>
                                    <?php foreach ($crops as $crop): ?>
                                        <option value="<?= $crop['id'] ?>">
                                            <?= esc($crop['crop_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Please select a crop.</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="village" class="form-label">Village <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="village" name="village" maxlength="100" required>
                                <div class="invalid-feedback">Please enter the village name.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="block_site" class="form-label">Block Site <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="block_site" name="block_site" maxlength="200" required>
                                <div class="invalid-feedback">Please enter the block site.</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="llg_id" class="form-label">LLG <span class="text-danger">*</span></label>
                                <select class="form-select" id="llg_id" name="llg_id" required onchange="filterWards()">
                                    <option value="">Select LLG</option>
                                    <?php foreach ($llgs as $llg): ?>
                                        <option value="<?= $llg['id'] ?>">
                                            <?= esc($llg['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Please select an LLG.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ward_id" class="form-label">Ward <span class="text-danger">*</span></label>
                                <select class="form-select" id="ward_id" name="ward_id" required disabled>
                                    <option value="">Select LLG first</option>
                                    <?php foreach ($wards as $ward): ?>
                                        <option value="<?= $ward['id'] ?>" data-llg="<?= $ward['llg_id'] ?>" style="display:none;">
                                            <?= esc($ward['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Please select a ward.</div>
                            </div>
                        </div>
                    </div>



                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="lat" class="form-label">Latitude</label>
                                <input type="text" class="form-control" id="lat" name="lat" maxlength="50" placeholder="e.g., -6.123456">
                                <small class="form-text text-muted">Optional GPS coordinate</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="lon" class="form-label">Longitude</label>
                                <input type="text" class="form-control" id="lon" name="lon" maxlength="50" placeholder="e.g., 147.123456">
                                <small class="form-text text-muted">Optional GPS coordinate</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                        <small class="form-text text-muted">Optional additional information</small>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="<?= base_url('staff/crops-farm-blocks') ?>" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Create Crops Farm Block
                        </button>
                    </div>

                <?= form_close() ?>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize Select2 for farmer dropdown
    $('.select2-farmer').select2({
        theme: 'bootstrap-5',
        width: '100%',
        placeholder: 'Search and select a farmer...',
        allowClear: true,
        minimumResultsForSearch: 0 // Always show search box
    });
});

// Filter wards based on selected LLG
function filterWards() {
    const llgSelect = document.getElementById('llg_id');
    const wardSelect = document.getElementById('ward_id');
    const selectedLlgId = llgSelect.value;

    // Clear ward selection
    wardSelect.value = '';

    if (selectedLlgId) {
        // Enable ward dropdown
        wardSelect.disabled = false;

        // Show only wards for selected LLG
        wardSelect.innerHTML = '<option value="">Select Ward</option>';

        // Get all ward options and show only matching ones
        <?php foreach ($wards as $ward): ?>
        if ('<?= $ward['llg_id'] ?>' === selectedLlgId) {
            wardSelect.innerHTML += '<option value="<?= $ward['id'] ?>"><?= esc($ward['name']) ?></option>';
        }
        <?php endforeach; ?>
    } else {
        // Disable ward dropdown if no LLG selected
        wardSelect.disabled = true;
        wardSelect.innerHTML = '<option value="">Select LLG first</option>';
    }
}



// Bootstrap validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?= $this->endSection() ?>
