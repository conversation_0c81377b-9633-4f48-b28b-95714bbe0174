<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3 d-flex justify-content-between">
        <a href="<?= base_url('staff/crops-farm-blocks') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Crops Farm Blocks
        </a>
        <div>
            <a href="<?= base_url('staff/crops-farm-blocks/' . $farm_block['id'] . '/edit') ?>" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
            <button type="button" class="btn btn-danger" onclick="confirmDelete(<?= $farm_block['id'] ?>)">
                <i class="fas fa-trash"></i> Delete
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-eye"></i> <?= esc($page_header) ?> - <?= esc($farm_block['block_code']) ?>
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Basic Information</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Block Code:</strong></td>
                                <td><?= esc($farm_block['block_code']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Farmer:</strong></td>
                                <td><?= esc($farm_block['farmer_name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Crop:</strong></td>
                                <td>
                                    <span class="badge" style="background-color: <?= esc($farm_block['crop_color_code']) ?>">
                                        <?= esc($farm_block['crop_name']) ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <?php if ($farm_block['status'] == 'active'): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php elseif ($farm_block['status'] == 'inactive'): ?>
                                        <span class="badge bg-warning">Inactive</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary"><?= ucfirst(esc($farm_block['status'])) ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">Location Information</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>District:</strong></td>
                                <td><?= esc($farm_block['district_name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>LLG:</strong></td>
                                <td><?= esc($farm_block['llg_name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Ward:</strong></td>
                                <td><?= esc($farm_block['ward_name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Village:</strong></td>
                                <td><?= esc($farm_block['village']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Block Site:</strong></td>
                                <td><?= esc($farm_block['block_site']) ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <?php if (!empty($farm_block['lat']) || !empty($farm_block['lon'])): ?>
                <div class="row mt-4">
                    <div class="col-12">
                        <h6 class="text-primary">GPS Coordinates</h6>
                        <table class="table table-borderless">
                            <?php if (!empty($farm_block['lat'])): ?>
                            <tr>
                                <td><strong>Latitude:</strong></td>
                                <td><?= esc($farm_block['lat']) ?></td>
                            </tr>
                            <?php endif; ?>
                            <?php if (!empty($farm_block['lon'])): ?>
                            <tr>
                                <td><strong>Longitude:</strong></td>
                                <td><?= esc($farm_block['lon']) ?></td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($farm_block['remarks'])): ?>
                <div class="row mt-4">
                    <div class="col-12">
                        <h6 class="text-primary">Remarks</h6>
                        <p class="text-muted"><?= nl2br(esc($farm_block['remarks'])) ?></p>
                    </div>
                </div>
                <?php endif; ?>

                <div class="row mt-4">
                    <div class="col-12">
                        <h6 class="text-primary">Record Information</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td><?= date('M d, Y H:i', strtotime($farm_block['created_at'])) ?></td>
                            </tr>
                            <?php if (!empty($farm_block['updated_at'])): ?>
                            <tr>
                                <td><strong>Last Updated:</strong></td>
                                <td><?= date('M d, Y H:i', strtotime($farm_block['updated_at'])) ?></td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= base_url('staff/crops-farm-blocks/' . $farm_block['id'] . '/edit') ?>" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit Farm Block
                    </a>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete(<?= $farm_block['id'] ?>)">
                        <i class="fas fa-trash"></i> Delete Farm Block
                    </button>
                    <hr>
                    <a href="<?= base_url('staff/crops-farm-blocks') ?>" class="btn btn-secondary">
                        <i class="fas fa-list"></i> View All Farm Blocks
                    </a>
                    <a href="<?= base_url('staff/crops-farm-blocks/create') ?>" class="btn btn-success">
                        <i class="fas fa-plus"></i> Add New Farm Block
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this crops farm block "<strong><?= esc($farm_block['block_code']) ?></strong>"? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = '<?= base_url('staff/crops-farm-blocks/') ?>' + id;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>

<?= $this->endSection() ?>
