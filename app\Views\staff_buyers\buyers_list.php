<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="row mb-3">
    <div class="col-md-12">
        <!-- Button to trigger Add Buyer Modal -->
        <button type="button" class="btn btn-success float-end" data-bs-toggle="modal" data-bs-target="#addBuyerModal">
            <i class="fas fa-plus-circle"></i> Add Buyer
        </button>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3 class="card-title"><?= esc($page_header ?? 'Crop Buyers') ?></h3>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-bordered" id="buyersTable" style="width:100%;">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Crop</th>
                        <th>Buyer Code</th>
                        <th>Name</th>
                        <th>Contact</th>
                        <th>Operation Span</th>
                        <th>Address</th>
                        <!-- <th>Status</th> -->
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($buyers)): ?>
                        <?php foreach ($buyers as $buyer): ?>
                            <tr id="buyer-row-<?= esc($buyer['id']) ?>">
                                <td><?= esc($buyer['id']) ?></td>
                                <td data-crop-id="<?= esc($buyer['crop_id']) ?>"><?= esc($buyer['crop_name'] ?? 'N/A') ?></td>
                                <td><?= esc($buyer['buyer_code']) ?></td>
                                <td><?= esc($buyer['name']) ?></td>
                                <td>
                                    <?= esc($buyer['contact_number']) ?>
                                    <?php if(!empty($buyer['email'])): ?>
                                        <br><small><?= esc($buyer['email']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?= ucfirst(esc($buyer['operation_span'])) ?></td>
                                <td><?= esc($buyer['address']) ?></td>
                                <!--
                                <td>
                                    <span class="badge bg-<?= $buyer['status'] === 'active' ? 'success' : ($buyer['status'] === 'deleted' ? 'danger' : 'secondary') ?>">
                                        <?= ucfirst(esc($buyer['status'])) ?>
                                    </span>
                                </td>
                                -->
                                <td>
                                    <!-- Edit Button: Triggers Edit Modal -->
                                    <button type="button" class="btn btn-sm btn-primary edit-btn" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#editBuyerModal"
                                            data-id="<?= esc($buyer['id']) ?>">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    
                                    <!-- Delete Button: Triggers AJAX delete -->
                                    <button type="button" class="btn btn-sm btn-danger delete-btn" 
                                            data-id="<?= esc($buyer['id']) ?>" 
                                            data-name="<?= esc($buyer['name']) ?>">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="8" class="text-center">No buyers found.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Include Add Buyer Modal -->
<?= $this->include('staff/buyers/buyers_add_modal') ?>

<!-- Include Edit Buyer Modal -->
<?= $this->include('staff/buyers/buyers_edit_modal') ?>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // --- CSRF Setup --- 
    var csrfTokenName = '<?= csrf_token() ?>'; // Get CSRF token name
    var csrfTokenValue = '<?= csrf_hash() ?>'; // Get CSRF hash

    // --- Initialize DataTable --- 
    var buyersTable = $('#buyersTable').DataTable({
        responsive: true,
        "order": [[ 1, "asc" ]], // Default order by Crop Name
        "columnDefs": [
            { "targets": [0], "visible": false, "searchable": false }, // Hide ID column but keep it available
            { "orderable": false, "targets": 7 } // Disable sorting on Actions column
        ]
    });

    // --- Add Buyer via AJAX --- 
    $('#addBuyerForm').submit(function(e) {
        e.preventDefault();
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const originalBtnText = $submitBtn.html();

        // Clear previous errors
        $form.find('.is-invalid').removeClass('is-invalid');
        $form.find('.invalid-feedback').remove();

        var formData = $form.serializeArray();
        // Add CSRF token to form data
        formData.push({name: csrfTokenName, value: csrfTokenValue});

        $.ajax({
            url: '<?= base_url('staff/buyers/add') ?>',
            type: 'POST',
            data: $.param(formData), // Use $.param to serialize correctly
            dataType: 'json',
            beforeSend: function() {
                $submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Saving...').prop('disabled', true);
            },
            success: function(response, textStatus, jqXHR) {
                // Update CSRF token value from response header or body if sent
                var newCsrfToken = jqXHR.getResponseHeader('X-CSRF-TOKEN') || response.csrf_token;
                if (newCsrfToken) {
                     csrfTokenValue = newCsrfToken;
                     // Update hidden CSRF input in both forms
                     $('input[name="' + csrfTokenName + '"]').val(csrfTokenValue);
                }

                if (response.status === 'success') {
                    toastr.success(response.message);
                    $('#addBuyerModal').modal('hide');
                    $form[0].reset(); // Clear the form

                    // --- Add new row to DataTable --- 
                    var newRow = buyersTable.row.add([
                        response.buyer.id,
                        response.buyer.crop_name, // Assuming crop_name is returned
                        response.buyer.buyer_code,
                        response.buyer.name,
                        response.buyer.contact_number + (response.buyer.email ? '<br><small>' + response.buyer.email + '</small>' : ''),
                        response.buyer.operation_span.charAt(0).toUpperCase() + response.buyer.operation_span.slice(1),
                        response.buyer.address,
                        // Actions column HTML
                        '<button type="button" class="btn btn-sm btn-primary edit-btn" data-bs-toggle="modal" data-bs-target="#editBuyerModal" data-id="' + response.buyer.id + '"><i class="fas fa-edit"></i> Edit</button> ' +
                        '<button type="button" class="btn btn-sm btn-danger delete-btn" data-id="' + response.buyer.id + '" data-name="' + response.buyer.name + '"><i class="fas fa-trash"></i> Delete</button>'
                    ]).draw(false).node(); // draw(false) prevents resetting pagination
                    
                    $(newRow).attr('id', 'buyer-row-' + response.buyer.id); // Add ID to the row
                    $(newRow).find('td').eq(1).attr('data-crop-id', response.buyer.crop_id); // Add crop ID data attribute

                } else if (response.status === 'error' && response.errors) {
                    // Display validation errors
                    toastr.error(response.message || 'Validation failed. Please check the form.');
                    $.each(response.errors, function(field, message) {
                        const $input = $form.find('[name="' + field + '"]');
                        $input.addClass('is-invalid');
                        $input.after('<div class="invalid-feedback">' + message + '</div>');
                    });
                } else {
                    toastr.error(response.message || 'An unexpected error occurred.');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                // Update CSRF token value from response header if available
                var newCsrfToken = jqXHR.getResponseHeader('X-CSRF-TOKEN');
                if (newCsrfToken) {
                    csrfTokenValue = newCsrfToken;
                    $('input[name="' + csrfTokenName + '"]').val(csrfTokenValue);
                }
                toastr.error('AJAX Error: ' + (jqXHR.responseJSON ? jqXHR.responseJSON.message : errorThrown));
            },
            complete: function() {
                $submitBtn.html(originalBtnText).prop('disabled', false);
            }
        });
    });

    // --- Edit Buyer: Populate Modal --- 
    $('#buyersTable tbody').on('click', '.edit-btn', function() {
        const buyerId = $(this).data('id');
        const $editForm = $('#editBuyerForm');

        // Clear previous errors
        $editForm.find('.is-invalid').removeClass('is-invalid');
        $editForm.find('.invalid-feedback').remove();

        // Fetch buyer data via AJAX
        $.ajax({
            url: '<?= base_url('staff/buyers/get/') ?>' + buyerId,
            type: 'GET',
            dataType: 'json',
            beforeSend: function() {
                // Optional: Show a loading indicator in the modal body
                 $editForm.find('.modal-body').addClass('loading-state'); 
            },
            success: function(response) {
                if (response.status === 'success' && response.buyer) {
                    const buyer = response.buyer;
                    // Populate the edit form
                    $editForm.find('#edit_buyer_id').val(buyer.id);
                    $editForm.find('#edit_crop_id').val(buyer.crop_id);
                    $editForm.find('#edit_name').val(buyer.name);
                    $editForm.find('#edit_contact_number').val(buyer.contact_number);
                    $editForm.find('#edit_email').val(buyer.email);
                    $editForm.find('#edit_operation_span').val(buyer.operation_span);
                    $editForm.find('#edit_address').val(buyer.address);
                    $editForm.find('#edit_description').val(buyer.description);
                    
                    // Ensure the modal is shown after data is populated
                    $('#editBuyerModal').modal('show'); 
                } else {
                    toastr.error(response.message || 'Could not fetch buyer details.');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                toastr.error('AJAX Error: ' + errorThrown);
            },
            complete: function() {
                $editForm.find('.modal-body').removeClass('loading-state');
            }
        });
    });

    // --- Update Buyer via AJAX --- 
    $('#editBuyerForm').submit(function(e) {
        e.preventDefault();
        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const originalBtnText = $submitBtn.html();
        const buyerId = $form.find('#edit_buyer_id').val();

        // Clear previous errors
        $form.find('.is-invalid').removeClass('is-invalid');
        $form.find('.invalid-feedback').remove();

        var formData = $form.serializeArray();
        // Add CSRF token
        formData.push({name: csrfTokenName, value: csrfTokenValue});

        $.ajax({
            url: '<?= base_url('staff/buyers/update') ?>',
            type: 'POST',
            data: $.param(formData),
            dataType: 'json',
            beforeSend: function() {
                $submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Updating...').prop('disabled', true);
            },
            success: function(response, textStatus, jqXHR) {
                // Update CSRF token
                var newCsrfToken = jqXHR.getResponseHeader('X-CSRF-TOKEN') || response.csrf_token;
                if (newCsrfToken) {
                     csrfTokenValue = newCsrfToken;
                     $('input[name="' + csrfTokenName + '"]').val(csrfTokenValue);
                }

                if (response.status === 'success') {
                    toastr.success(response.message);
                    $('#editBuyerModal').modal('hide');

                    // --- Update row in DataTable --- 
                    var updatedRowData = [
                        response.buyer.id,
                        response.buyer.crop_name, // Make sure controller returns crop_name
                        response.buyer.buyer_code,
                        response.buyer.name,
                        response.buyer.contact_number + (response.buyer.email ? '<br><small>' + response.buyer.email + '</small>' : ''),
                        response.buyer.operation_span.charAt(0).toUpperCase() + response.buyer.operation_span.slice(1),
                        response.buyer.address,
                        // Actions column HTML (remains the same essentially, but use updated data)
                        '<button type="button" class="btn btn-sm btn-primary edit-btn" data-bs-toggle="modal" data-bs-target="#editBuyerModal" data-id="' + response.buyer.id + '"><i class="fas fa-edit"></i> Edit</button> ' +
                        '<button type="button" class="btn btn-sm btn-danger delete-btn" data-id="' + response.buyer.id + '" data-name="' + response.buyer.name + '"><i class="fas fa-trash"></i> Delete</button>'
                    ];
                    
                    var rowNode = buyersTable.row('#buyer-row-' + buyerId).data(updatedRowData).draw(false).node();
                    $(rowNode).find('td').eq(1).attr('data-crop-id', response.buyer.crop_id); // Update crop ID data attribute

                } else if (response.status === 'error' && response.errors) {
                    // Display validation errors
                     toastr.error(response.message || 'Validation failed. Please check the form.');
                     $.each(response.errors, function(field, message) {
                        // Adjust field name if needed (e.g., remove prefix)
                        let inputName = field.startsWith('edit_') ? field : 'edit_' + field; 
                        const $input = $form.find('[name="' + inputName + '"]');
                        $input.addClass('is-invalid');
                        $input.after('<div class="invalid-feedback">' + message + '</div>');
                     });
                } else {
                    toastr.error(response.message || 'An unexpected error occurred.');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                 // Update CSRF token
                 var newCsrfToken = jqXHR.getResponseHeader('X-CSRF-TOKEN');
                 if (newCsrfToken) {
                     csrfTokenValue = newCsrfToken;
                     $('input[name="' + csrfTokenName + '"]').val(csrfTokenValue);
                 }
                toastr.error('AJAX Error: ' + (jqXHR.responseJSON ? jqXHR.responseJSON.message : errorThrown));
            },
            complete: function() {
                $submitBtn.html(originalBtnText).prop('disabled', false);
            }
        });
    });

    // --- Delete Buyer via AJAX --- 
    $('#buyersTable tbody').on('click', '.delete-btn', function() {
        const buyerId = $(this).data('id');
        const buyerName = $(this).data('name');

        // Use SweetAlert for confirmation
        Swal.fire({
            title: 'Are you sure?',
            html: `Do you really want to delete buyer "<strong>${buyerName}</strong>" (ID: ${buyerId})? <br><small>This action marks the buyer as deleted but does not remove data permanently.</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                var postData = {};
                postData[csrfTokenName] = csrfTokenValue;

                $.ajax({
                    url: '<?= base_url('staff/buyers/delete/') ?>' + buyerId,
                    type: 'POST', // Use POST to send CSRF token easily
                    data: postData,
                    dataType: 'json',
                    beforeSend: function() {
                        // Optional: Show loading state on button or row
                    },
                    success: function(response, textStatus, jqXHR) {
                         // Update CSRF token
                         var newCsrfToken = jqXHR.getResponseHeader('X-CSRF-TOKEN') || response.csrf_token;
                         if (newCsrfToken) {
                             csrfTokenValue = newCsrfToken;
                             $('input[name="' + csrfTokenName + '"]').val(csrfTokenValue);
                         }

                        if (response.status === 'success') {
                            toastr.success(response.message);
                            // --- Remove row from DataTable --- 
                            buyersTable.row('#buyer-row-' + buyerId).remove().draw(false);
                        } else {
                            toastr.error(response.message || 'Failed to delete buyer.');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                         // Update CSRF token
                         var newCsrfToken = jqXHR.getResponseHeader('X-CSRF-TOKEN');
                         if (newCsrfToken) {
                             csrfTokenValue = newCsrfToken;
                             $('input[name="' + csrfTokenName + '"]').val(csrfTokenValue);
                         }
                        toastr.error('AJAX Error: ' + (jqXHR.responseJSON ? jqXHR.responseJSON.message : errorThrown));
                    },
                    complete: function() {
                        // Optional: Remove loading state
                    }
                });
            }
        });
    });

    // --- Clear Add Modal on Hide --- 
    $('#addBuyerModal').on('hidden.bs.modal', function () {
        $(this).find('form')[0].reset();
        $(this).find('.is-invalid').removeClass('is-invalid');
        $(this).find('.invalid-feedback').remove();
    });
    
    // --- Clear Edit Modal on Hide --- 
    $('#editBuyerModal').on('hidden.bs.modal', function () {
        $(this).find('form')[0].reset();
        $(this).find('.is-invalid').removeClass('is-invalid');
        $(this).find('.invalid-feedback').remove();
        // Clear any potential loading states if used
        $(this).find('.modal-body').removeClass('loading-state'); 
    });

});
</script>
<?= $this->endSection() ?> 