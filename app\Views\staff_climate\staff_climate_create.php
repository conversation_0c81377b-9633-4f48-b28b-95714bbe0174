<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?= $page_header ?? 'Add New Climate Focus' ?></h5>
                <a href="<?= base_url('staff/tools/climate-data') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to List
                </a>
            </div>
            <div class="card-body">
                <?php if (session()->has('errors')): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach (session('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('staff/tools/climate-data') ?>" method="post">
                    <?= csrf_field() ?>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="<?= old('location') ?>" required>
                            <div class="form-text">Enter a descriptive name for this climate focus location</div>
                        </div>

                        <div class="col-md-6">
                            <label for="gps" class="form-label">GPS Coordinates <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="gps" name="gps" value="<?= old('gps') ?>" required>
                            <div class="form-text">Enter GPS coordinates (e.g., -6.314993, 143.95555)</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-12">
                            <label for="remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="4"><?= old('remarks') ?></textarea>
                            <div class="form-text">Additional notes or observations about this climate focus area</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 d-flex justify-content-end">
                            <button type="reset" class="btn btn-secondary me-2">Reset</button>
                            <button type="submit" class="btn btn-primary">Save Climate Focus</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Form validation
        $('form').on('submit', function(e) {
            let isValid = true;

            // Check required fields
            $(this).find('[required]').each(function() {
                if ($(this).val().trim() === '') {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                toastr.error('Please fill in all required fields');
            }
        });

        // Remove validation styling on input
        $('input, textarea').on('input', function() {
            $(this).removeClass('is-invalid');
        });
    });
</script>
<?= $this->endSection() ?>
