<?= $this->extend('templates/staff_template') ?>
<?= $this->section('content') ?>

<div class="row">
    <div class="col-12">
        <!-- Navigation -->
        <div class="mb-3">
            <a href="<?= base_url('staff/crops/data-show/' . $block['id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Crops Data
            </a>
        </div>

        <!-- Form Card -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i><?= esc($page_header) ?>
                </h5>
                <p class="mb-0 small"><?= esc($page_desc) ?></p>
            </div>
            <div class="card-body">
                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Farm Block Information -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Farm Block Information</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>Block Code:</strong> <?= esc($block['block_code']) ?>
                                </div>
                                <div class="col-md-4">
                                    <strong>Farmer:</strong> <?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?>
                                </div>
                                <div class="col-md-4">
                                    <strong>Crop:</strong> <span class="badge bg-success"><?= esc($crop['crop_name']) ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form -->
                <?= form_open('staff/crops/data-store', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                    <input type="hidden" name="block_id" value="<?= $block['id'] ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="action_type" class="form-label">Action Type <span class="text-danger">*</span></label>
                                <select class="form-select" name="action_type" id="action_type" required>
                                    <option value="">Select action type</option>
                                    <option value="add" <?= old('action_type') == 'add' ? 'selected' : '' ?>>Add</option>
                                    <option value="remove" <?= old('action_type') == 'remove' ? 'selected' : '' ?>>Remove</option>
                                </select>
                                <div class="invalid-feedback">
                                    Please select an action type.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="action_reason" class="form-label">Action Reason <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="action_reason" name="action_reason" 
                                       value="<?= old('action_reason') ?>" required maxlength="100">
                                <div class="form-text">e.g., new planting, disease, disaster, etc.</div>
                                <div class="invalid-feedback">
                                    Please provide an action reason.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="action_date" class="form-label">Action Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="action_date" name="action_date" 
                                       value="<?= old('action_date') ?>" required max="<?= date('Y-m-d') ?>">
                                <div class="invalid-feedback">
                                    Please select a valid action date.
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="number_of_plants" class="form-label">Number of Plants <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="number_of_plants" name="number_of_plants" 
                                       value="<?= old('number_of_plants') ?>" required min="1">
                                <div class="invalid-feedback">
                                    Please enter a valid number of plants.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="breed" class="form-label">Breed</label>
                                <input type="text" class="form-control" id="breed" name="breed" 
                                       value="<?= old('breed') ?>" maxlength="255">
                                <div class="form-text">Crop variety or breed information</div>
                            </div>

                            <div class="mb-3">
                                <label for="hectares" class="form-label">Hectares <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" class="form-control" id="hectares" name="hectares" 
                                       value="<?= old('hectares') ?>" required min="0.01">
                                <div class="invalid-feedback">
                                    Please enter a valid hectare value.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                  placeholder="Additional notes or comments..."><?= old('remarks') ?></textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="<?= base_url('staff/crops/data-show/' . $block['id']) ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>Save Crops Data
                        </button>
                    </div>
                <?= form_close() ?>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Set default date to today
    if (!$('#action_date').val()) {
        $('#action_date').val(new Date().toISOString().split('T')[0]);
    }
});
</script>

<?= $this->endSection() ?>
