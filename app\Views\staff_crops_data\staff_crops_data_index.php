<?= $this->extend('templates/staff_template') ?>
<?= $this->section('content') ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-seedling me-2"></i><?= esc($page_header) ?>
                </h5>
                <p class="mb-0 small"><?= esc($page_desc) ?></p>
            </div>
            <div class="card-body">
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($farm_blocks): ?>
                    <div class="table-responsive">
                        <table class="table table-hover" id="farmBlocksTable">
                            <thead class="table-light">
                                <tr>
                                    <th class="text-nowrap">#</th>
                                    <th class="text-nowrap">Block Code</th>
                                    <th class="text-nowrap">Crop</th>
                                    <th class="text-nowrap">Location</th>
                                    <th class="text-nowrap">Farmer</th>
                                    <th class="text-nowrap">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($farm_blocks as $index => $block): ?>
                                    <tr class="align-middle">
                                        <td class="text-nowrap"><?= $index + 1 ?></td>
                                        <td class="text-nowrap">
                                            <strong><?= esc($block['block_code']) ?></strong>
                                        </td>
                                        <td class="text-nowrap">
                                            <?php
                                            foreach ($crops as $crop):
                                                if ($crop['id'] == $block['crop_id']):
                                                    echo '<span class="badge bg-success">' . esc($crop['crop_name']) . '</span>';
                                                endif;
                                            endforeach;
                                            ?>
                                        </td>
                                        <td class="text-nowrap">
                                            <?= esc($block['village']) ?> - <?= esc($block['block_site']) ?>
                                            <br><small class="text-muted"><?= esc($block['ward_name']) ?>, <?= esc($block['llg_name']) ?></small>
                                        </td>
                                        <td class="text-nowrap">
                                            <?php
                                            foreach ($farmers as $farmer):
                                                if ($farmer['id'] == $block['farmer_id']):
                                                    echo esc($farmer['given_name'] . ' ' . $farmer['surname']);
                                                endif;
                                            endforeach;
                                            ?>
                                        </td>
                                        <td class="text-nowrap">
                                            <div class="btn-group" role="group">
                                                <a href="<?= base_url('staff/crops-data/' . $block['id']) ?>" 
                                                   class="btn btn-sm btn-primary" 
                                                   title="View Crops Data">
                                                    <i class="fas fa-eye"></i> View Data
                                                </a>
                                                <a href="<?= base_url('staff/crops-data/' . $block['id'] . '/create') ?>" 
                                                   class="btn btn-sm btn-success" 
                                                   title="Add Crops Data">
                                                    <i class="fas fa-plus"></i> Add Data
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Farm Blocks Found</h5>
                        <p class="text-muted">No active farm blocks found for your district. Please create farm blocks first.</p>
                        <a href="<?= base_url('staff/crops-farm-blocks') ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Manage Farm Blocks
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable if there are records
    <?php if ($farm_blocks): ?>
    $('#farmBlocksTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[1, 'asc']], // Sort by block code
        columnDefs: [
            { orderable: false, targets: [5] } // Disable sorting on Actions column
        ],
        language: {
            search: "Search farm blocks:",
            lengthMenu: "Show _MENU_ blocks per page",
            info: "Showing _START_ to _END_ of _TOTAL_ farm blocks",
            infoEmpty: "No farm blocks available",
            infoFiltered: "(filtered from _MAX_ total blocks)"
        }
    });
    <?php endif; ?>
});
</script>

<?= $this->endSection() ?>
