<?= $this->extend('templates/staff_template') ?>
<?= $this->section('content') ?>

<div class="row">
    <div class="col-12">
        <!-- Navigation -->
        <div class="mb-3">
            <a href="<?= base_url('staff/crops-data') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Farm Blocks
            </a>
            <a href="<?= base_url('staff/crops-data/' . $block['id'] . '/create') ?>" class="btn btn-success float-end">
                <i class="fas fa-plus me-2"></i>Add Crops Data
            </a>
        </div>

        <!-- Farm Block Information -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Farm Block Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Block Code:</strong></td>
                                <td><?= esc($block['block_code']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Farmer:</strong></td>
                                <td><?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Crop:</strong></td>
                                <td><span class="badge bg-success"><?= esc($crop['crop_name']) ?></span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Location:</strong></td>
                                <td><?= esc($block['village']) ?> - <?= esc($block['block_site']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Ward:</strong></td>
                                <td><?= esc($ward['name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>LLG:</strong></td>
                                <td><?= esc($llg['name']) ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4><?= number_format($total_plants_added['total_plants_added'] ?? 0) ?></h4>
                        <p class="mb-0">Plants Added</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h4><?= number_format($total_plants_removed['total_plants_removed'] ?? 0) ?></h4>
                        <p class="mb-0">Plants Removed</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4><?= number_format($total_hectares_added['total_hectares_added'] ?? 0, 2) ?></h4>
                        <p class="mb-0">Hectares Added</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4><?= number_format($total_hectares_removed['total_hectares_removed'] ?? 0, 2) ?></h4>
                        <p class="mb-0">Hectares Removed</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Crops Data Table -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Crops Data Records
                </h5>
            </div>
            <div class="card-body">
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($crops_data): ?>
                    <div class="table-responsive">
                        <table class="table table-hover" id="cropsDataTable">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>Action Type</th>
                                    <th>Action Date</th>
                                    <th>Plants</th>
                                    <th>Hectares</th>
                                    <th>Breed</th>
                                    <th>Reason</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($crops_data as $index => $data): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <td>
                                            <?php if ($data['action_type'] == 'add'): ?>
                                                <span class="badge bg-success">Add</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Remove</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= date('M d, Y', strtotime($data['action_date'])) ?></td>
                                        <td><?= number_format($data['number_of_plants']) ?></td>
                                        <td><?= number_format($data['hectares'], 2) ?></td>
                                        <td><?= esc($data['breed']) ?></td>
                                        <td><?= esc($data['action_reason']) ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?= base_url('staff/crops-data/edit/' . $data['id']) ?>" 
                                                   class="btn btn-sm btn-warning" 
                                                   title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="<?= base_url('staff/crops-data/delete/' . $data['id']) ?>" 
                                                   class="btn btn-sm btn-danger" 
                                                   title="Delete"
                                                   onclick="return confirm('Are you sure you want to delete this record?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Crops Data Found</h5>
                        <p class="text-muted">No crops data records found for this farm block.</p>
                        <a href="<?= base_url('staff/crops-data/' . $block['id'] . '/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Add First Record
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable if there are records
    <?php if ($crops_data): ?>
    $('#cropsDataTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[2, 'desc']], // Sort by action date descending
        columnDefs: [
            { orderable: false, targets: [7] } // Disable sorting on Actions column
        ],
        language: {
            search: "Search crops data:",
            lengthMenu: "Show _MENU_ records per page",
            info: "Showing _START_ to _END_ of _TOTAL_ records",
            infoEmpty: "No records available",
            infoFiltered: "(filtered from _MAX_ total records)"
        }
    });
    <?php endif; ?>
});
</script>

<?= $this->endSection() ?>
