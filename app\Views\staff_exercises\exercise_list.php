<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Exercises Header -->
<div class="row mb-4">
    <div class="col-md-6">
        <h2><i class="fas fa-clipboard-list me-2"></i>Exercises</h2>
        <p class="text-muted">Manage field exercises and data collection activities</p>
    </div>
    <div class="col-md-6 text-end">
        <a href="<?= base_url('exercises/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus-circle me-2"></i>Create New Exercise
        </a>
    </div>
</div>

<!-- Exercises List -->
<div class="card">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0"><i class="fas fa-clipboard-check me-2"></i>Current Exercises</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="exercisesTable">
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>District</th>
                        <th>Date Range</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($exercises)) : ?>
                        <?php foreach ($exercises as $exercise) : ?>
                            <tr>
                                <td><?= esc($exercise['title']) ?></td>
                                <td><?= esc($exercise['district_name'] ?? 'N/A') ?></td>
                                <td><?= date('M d, Y', strtotime($exercise['date_from'])) ?> - <?= date('M d, Y', strtotime($exercise['date_to'])) ?></td>
                                <td>
                                    <?php
                                    $statusBadge = 'secondary';
                                    switch ($exercise['status']) {
                                        case 'active':
                                            $statusBadge = 'success';
                                            break;
                                        case 'draft':
                                            $statusBadge = 'warning';
                                            break;
                                        case 'cancelled':
                                            $statusBadge = 'danger';
                                            break;
                                        case 'submitted':
                                            $statusBadge = 'info';
                                            break;
                                        case 'approved':
                                            $statusBadge = 'primary';
                                            break;
                                    }
                                    ?>
                                    <span class="badge bg-<?= $statusBadge ?>"><?= ucfirst(esc($exercise['status'])) ?></span>
                                </td>
                                <td>
                                    <a href="<?= base_url('exercises/view/' . $exercise['id']) ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= base_url('exercises/edit/' . $exercise['id']) ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else : ?>
                        <tr>
                            <td colspan="5" class="text-center">No exercises found. <a href="<?= base_url('exercises/create') ?>">Create your first exercise</a>.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Only initialize DataTable if there are rows in the table (excluding the empty message row)
    if ($('#exercisesTable tbody tr').length > 0 && !$('#exercisesTable tbody tr:first').find('td[colspan]').length) {
        $('#exercisesTable').DataTable({
            responsive: true,
            order: [[3, 'asc']]
        });
    }
});
</script>
<?= $this->endSection() ?> 