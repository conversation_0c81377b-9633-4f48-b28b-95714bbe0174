<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped text-nowrap" id="farmBlocksTable">
                <thead>
                    <tr>
                        <th>Block Code</th>
                        <th>Farmer</th>
                        <th>Crop</th>
                        <th>Location</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($farm_blocks as $block): ?>
                        <tr>
                            <td><?= esc($block['block_code']) ?></td>
                            <td><?= esc($block['given_name']) ?> <?= esc($block['surname']) ?></td>
                            <td><?= esc($block['crop_name']) ?></td>
                            <td>
                                <?= esc($block['district_name']) ?> / 
                                <?= esc($block['llg_name']) ?> / 
                                <?= esc($block['ward_name']) ?>
                            </td>
                            <td>
                                <a href="<?= base_url('staff/farms/view-pesticides-data/' . $block['id']) ?>" 
                                   class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> View Pesticides Data
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    $('#farmBlocksTable').DataTable({
        responsive: false,
        processing: true,
        order: [[0, 'desc']]
    });
});
</script>
<?= $this->endSection() ?> 