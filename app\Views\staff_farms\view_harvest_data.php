<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3 d-flex justify-content-between">
        <a href="<?= base_url('staff/farms/harvest_data') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Harvest Data
        </a>
        <div>
            <button type="button" class="btn btn-success float-end" data-bs-toggle="modal" data-bs-target="#addHarvestDataModal">
                <i class="fas fa-plus-circle"></i> Add Harvest Data
            </button>
        </div>
    </div>
</div>

<!-- Block Details Cards -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Block Code:</strong> <?= esc($block['block_code'] ?? 'N/A') ?></p>
                <p><strong>Crop:</strong> <?= esc($crop['crop_name'] ?? 'N/A') ?></p>
                <p><strong>Farmer:</strong> <?= esc($farmer['given_name'] ?? '') . ' ' . esc($farmer['surname'] ?? '') ?></p>
                <p><strong>Remarks:</strong> <?= esc($block['remarks']) ?: 'No remarks' ?></p>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Location Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Village:</strong> <?= esc($block['village']) ?></p>
                <p><strong>Block Site:</strong> <?= esc($block['block_site']) ?></p>
                <p><strong>Province:</strong> <?= esc($province['name']) ?>, <?= esc($district['name']) ?>, <?= esc($llg['name']) ?>, <?= esc($ward['name']) ?></p>
                <p><strong>Coordinates:</strong> <?= esc($block['lon']) ?>, <?= esc($block['lat']) ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Harvest Data Table -->
<div class="card">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-history"></i> Harvest History</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-striped text-nowrap" id="harvestDataTable">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Item</th>
                        <th>Description</th>
                        <th>Unit</th>
                        <th>Unit of Measure</th>
                        <th>Quantity</th>
                        <th>Recorded By</th>
                        <th>Remarks</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($harvest_data as $data): ?>
                        <tr>
                            <td><?= date('d M Y', strtotime($data['harvest_date'])) ?></td>
                            <td><?= esc($data['item']) ?></td>
                            <td><?= esc($data['description']) ?></td>
                            <td><?= esc($data['unit']) ?></td>
                            <td><?= esc($data['unit_of_measure']) ?></td>
                            <td class="text-end"><?= number_format($data['quantity'], 2) ?></td>
                            <td><?php
                                foreach ($users as $user) {
                                    if ($user['id'] === $data['created_by']) {
                                        echo esc($user['name']);
                                        break;
                                    }
                                }
                                ?></td>
                            <td><?= esc($data['remarks']) ?: '-' ?></td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary edit-btn"
                                    data-bs-toggle="modal"
                                    data-bs-target="#editHarvestDataModal"
                                    data-id="<?= $data['id'] ?>"
                                    data-item="<?= $data['item'] ?>"
                                    data-description="<?= $data['description'] ?>"
                                    data-quantity="<?= $data['quantity'] ?>"
                                    data-unit="<?= $data['unit'] ?>"
                                    data-unit_of_measure="<?= $data['unit_of_measure'] ?>"
                                    data-harvest_date="<?= $data['harvest_date'] ?>"
                                    data-remarks="<?= $data['remarks'] ?>">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Harvest Data Modal -->
<div class="modal fade" id="addHarvestDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Add Harvest Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open('staff/farms/add-harvest-data', ['id' => 'addHarvestForm']) ?>
            <div class="modal-body">
                <input type="hidden" name="block_id" value="<?= $block['id'] ?>">
                <input type="hidden" name="crop_id" value="<?= $block['crop_id'] ?>">

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="item" class="form-label">Item Name *</label>
                        <input type="text" class="form-control" id="item" name="item" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="harvest_date" class="form-label">Harvest Date *</label>
                        <input type="date" class="form-control" id="harvest_date" name="harvest_date" required>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="unit" class="form-label">Unit *</label>
                            <input type="number" step="0.01" class="form-control" id="unit" name="unit" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="unit_of_measure" class="form-label">Unit of Measure *</label>
                            <input type="text" class="form-control" id="unit_of_measure" name="unit_of_measure" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="quantity" class="form-label">Quantity *</label>
                            <input type="number" step="0.01" class="form-control" id="quantity" name="quantity" required>
                        </div>
                    </div>
                </div>


                <div class="mb-3">
                    <label for="remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Save Data</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Harvest Data Modal -->
<div class="modal fade" id="editHarvestDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Edit Harvest Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open('staff/farms/update-harvest-data', ['id' => 'editHarvestForm']) ?>
            <div class="modal-body">
                <input type="hidden" name="id" id="edit_id">
                <input type="hidden" name="block_id" value="<?= $block['id'] ?>">
                <input type="hidden" name="crop_id" value="<?= $block['crop_id'] ?>">

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_item" class="form-label">Item Name *</label>
                            <input type="text" class="form-control" id="edit_item" name="item" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_harvest_date" class="form-label">Harvest Date *</label>
                            <input type="date" class="form-control" id="edit_harvest_date" name="harvest_date" required>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="edit_description" class="form-label">Description</label>
                    <textarea class="form-control" id="edit_description" name="description" rows="2"></textarea>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="edit_unit" class="form-label">Unit *</label>
                            <input type="number" step="0.01" class="form-control" id="edit_unit" name="unit" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="edit_unit_of_measure" class="form-label">Unit of Measure *</label>
                            <input type="text" class="form-control" id="edit_unit_of_measure" name="unit_of_measure" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="edit_quantity" class="form-label">Quantity *</label>
                            <input type="number" step="0.01" class="form-control" id="edit_quantity" name="quantity" required>
                        </div>
                    </div>

                </div>

                <div class="mb-3">
                    <label for="edit_remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Update Data</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#harvestDataTable').DataTable({
            responsive: false,
            processing: true,
            order: [
                [0, 'desc']
            ]
        });

        // Form submission handler for add form
        $('#addHarvestForm').submit(function(e) {
            e.preventDefault();
            submitForm($(this), 'add');
        });

        // Form submission handler for edit form
        $('#editHarvestForm').submit(function(e) {
            e.preventDefault();
            submitForm($(this), 'update');
        });

        function submitForm($form, action) {
            if (!validateForm($form)) {
                return;
            }

            const submitBtn = $form.find('button[type="submit"]');
            const originalText = submitBtn.html();
            const loadingText = action === 'add' ?
                '<i class="fas fa-spinner fa-spin"></i> Saving...' :
                '<i class="fas fa-spinner fa-spin"></i> Updating...';

            $.ajax({
                url: $form.attr('action'),
                type: 'POST',
                data: $form.serialize(),
                dataType: 'json',
                beforeSend: function() {
                    submitBtn.prop('disabled', true).html(loadingText);
                },
                success: function(response) {
                    if (response.status === 'success') {
                        toastr.success(response.message);
                        if (action === 'add') {
                            $form[0].reset();
                            $('#addHarvestDataModal').modal('hide');
                        } else {
                            $('#editHarvestDataModal').modal('hide');
                        }
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error(error);
                    toastr.error('An error occurred while processing your request');
                },
                complete: function() {
                    submitBtn.prop('disabled', false).html(originalText);
                }
            });
        }

        function validateForm($form) {
            let isValid = true;
            $form.find('.is-invalid').removeClass('is-invalid');

            const requiredFields = [
                'item',
                'quantity',
                'unit',
                'unit_of_measure',
                'harvest_date'
            ];

            requiredFields.forEach(field => {
                const $field = $form.find(`[name="${field}"]`);
                if (!$field.val()) {
                    $field.addClass('is-invalid');
                    isValid = false;
                }
            });

            // Validate numbers are positive
            const quantity = parseFloat($form.find('[name="quantity"]').val());
            const unit = parseFloat($form.find('[name="unit"]').val());

            if (quantity <= 0) {
                $form.find('[name="quantity"]').addClass('is-invalid');
                isValid = false;
            }

            if (unit <= 0) {
                $form.find('[name="unit"]').addClass('is-invalid');
                isValid = false;
            }

            if (!isValid) {
                toastr.error('Please fill in all required fields correctly');
            }

            return isValid;
        }

        // Handle edit button click
        $('.edit-btn').click(function() {
            const data = $(this).data();

            // Populate form fields
            $('#edit_id').val(data.id);
            $('#edit_item').val(data.item);
            $('#edit_description').val(data.description);
            $('#edit_quantity').val(data.quantity);
            $('#edit_unit').val(data.unit);
            $('#edit_unit_of_measure').val(data.unit_of_measure);
            $('#edit_harvest_date').val(data.harvest_date);
            $('#edit_remarks').val(data.remarks);
        });
    });
</script>
<?= $this->endSection() ?>