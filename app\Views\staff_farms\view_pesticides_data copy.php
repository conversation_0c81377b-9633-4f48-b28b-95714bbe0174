<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-md-12 mb-3 d-flex justify-content-between">
        <a href="<?= base_url('staff/farms/pesticides_data') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Pesticides Data
        </a>
        <div>
            <button type="button" class="btn btn-success float-end" data-bs-toggle="modal" data-bs-target="#addPesticidesDataModal">
                <i class="fas fa-plus-circle"></i> Add Pesticides Data
            </button>
        </div>
    </div>
</div>

<!-- Block Details Cards -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Block Code:</strong> <?= esc($block['block_code']) ?></p>
                <p><strong>Crop:</strong> <?= esc($crop['crop_name']) ?></p>
                <p><strong>Farmer:</strong> <?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></p>
                <p><strong>Remarks:</strong> <?= esc($block['remarks']) ?: 'No remarks' ?></p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Location Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Village:</strong> <?= esc($block['village']) ?></p>
                <p><strong>Block Site:</strong> <?= esc($block['block_site']) ?></p>
                <p><strong>Province:</strong> <?= esc($province['name']) ?>, <?= esc($district['name']) ?>, <?= esc($llg['name']) ?>, <?= esc($ward['name']) ?></p>
                <p><strong>Coordinates:</strong> <?= esc($block['lon']) ?>, <?= esc($block['lat']) ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Pesticides Data Table -->
<div class="card">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-history"></i> Pesticides Application History</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="pesticidesDataTable">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Pesticide Type</th>
                        <th>Name</th>
                        <th>Brand</th>
                        <th>Unit</th>
                        <th>Quantity</th>
                        <th>UoM</th>
                        <th>Recorded By</th>
                        <th>Remarks</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pesticides_data as $data): ?>
                        <tr>
                            <td><?= date('d M Y', strtotime($data['action_date'])) ?></td>
                            <td><?php
                                foreach ($pesticides as $pesticide) {
                                    if ($pesticide['value'] === $data['pesticide_id']) {
                                        echo esc($pesticide['crop_name']);
                                        break;
                                    }
                                }
                            ?></td>
                            <td><?= esc($data['name']) ?></td>
                            <td><?= esc($data['brand']) ?></td>
                            <td><?= esc($data['unit']) ?></td>
                            <td><?= number_format($data['quantity'], 2) ?></td>
                            <td><?= esc($data['unit_of_measure']) ?></td>
                            <td><?php
                                foreach ($users as $user) {
                                    if ($user['id'] === $data['created_by']) {
                                        echo esc($user['name']);
                                        break;
                                    }
                                }
                            ?></td>
                            <td><?= esc($data['remarks']) ?: '-' ?></td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary edit-btn" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#editPesticidesDataModal"
                                        data-id="<?= $data['id'] ?>"
                                        data-pesticide_type_id="<?= $data['pesticide_id'] ?>"
                                        data-name="<?= $data['name'] ?>"
                                        data-brand="<?= $data['brand'] ?>"
                                        data-unit="<?= $data['unit'] ?>"
                                        data-quantity="<?= $data['quantity'] ?>"
                                        data-unit_of_measure="<?= $data['unit_of_measure'] ?>"
                                        data-action_date="<?= $data['action_date'] ?>"
                                        data-remarks="<?= $data['remarks'] ?>">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Pesticides Data Modal -->
<div class="modal fade" id="addPesticidesDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Add Pesticides Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open('staff/farms/add-pesticides-data', ['id' => 'addPesticidesForm']) ?>
            <div class="modal-body">
                <input type="hidden" name="block_id" value="<?= $block['id'] ?>">
                <input type="hidden" name="crop_id" value="<?= $block['crop_id'] ?>">

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="action_date" class="form-label">Application Date *</label>
                        <input type="date" class="form-control" id="action_date" name="action_date" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="pesticide_id" class="form-label">Pesticide Type *</label>
                        <select name="pesticide_id" id="pesticide_id" class="form-select" required>
                            <option value="">Select Pesticide</option>
                            <?php foreach ($pesticides as $pesticide): ?>
                                <option value="<?= $pesticide['id'] ?>" 
                                        data-hint="<?= esc($pesticide['remarks']) ?>">
                                    <?= esc($pesticide['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small class="text-success" id="pesticide_id_help"></small>
                        <script>
                            $('#pesticide_id').on('change', function() {
                                const hint = $(this).find(':selected').data('hint');
                                $('#pesticide_id_help').text(hint || '');
                            });
                        </script>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Pesticide Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="brand" class="form-label">Brand *</label>
                            <input type="text" class="form-control" id="brand" name="brand" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                <div class="col-md-4">
                        <div class="mb-3">
                            <label for="unit" class="form-label">Unit *</label>
                            <input type="number" step="0.01" class="form-control" id="unit" name="unit" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="unit_of_measure" class="form-label">Unit of Measure *</label>
                            <select class="form-select" id="unit_of_measure" name="unit_of_measure" required>
                                <option value="">Select Unit</option>
                                <option value="kg">Kilogram (kg)</option>
                                <option value="g">Gram (g)</option>
                                <option value="l">Liter (l)</option>
                                <option value="ml">Milliliter (ml)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="quantity" class="form-label">Quantity *</label>
                            <input type="number" step="0.01" class="form-control" id="quantity" name="quantity" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Save Data</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Pesticides Data Modal -->
<div class="modal fade" id="editPesticidesDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Edit Pesticides Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open('staff/farms/update-pesticides-data', ['id' => 'editPesticidesForm']) ?>
            <div class="modal-body">
                <input type="hidden" name="id" id="edit_id">
                <input type="hidden" name="block_id" value="<?= $block['id'] ?>">
                <input type="hidden" name="crop_id" value="<?= $block['crop_id'] ?>">

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="edit_pesticide_type_id" class="form-label">Pesticide Type *</label>
                        <select name="pesticide_type_id" id="edit_pesticide_type_id" class="form-select" required>
                            <option value="">Select Pesticide</option>
                            <?php foreach ($pesticides as $pesticide): ?>
                                <option value="<?= $pesticide['id'] ?>" 
                                        data-hint="<?= esc($pesticide['remarks']) ?>">
                                    <?= esc($pesticide['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small class="text-success" id="edit_pesticide_type_id_help"></small>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_name" class="form-label">Pesticide Name *</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="edit_brand" class="form-label">Brand *</label>
                            <input type="text" class="form-control" id="edit_brand" name="brand" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="edit_quantity" class="form-label">Quantity *</label>
                            <input type="number" step="0.01" class="form-control" id="edit_quantity" name="quantity" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="edit_unit_of_measure" class="form-label">Unit of Measure *</label>
                            <select class="form-select" id="edit_unit_of_measure" name="unit_of_measure" required>
                                <option value="">Select Unit</option>
                                <option value="kg">Kilogram (kg)</option>
                                <option value="g">Gram (g)</option>
                                <option value="l">Liter (l)</option>
                                <option value="ml">Milliliter (ml)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="edit_unit" class="form-label">Unit *</label>
                            <input type="number" step="0.01" class="form-control" id="edit_unit" name="unit" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="edit_action_date" class="form-label">Application Date *</label>
                            <input type="date" class="form-control" id="edit_action_date" name="action_date" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="edit_remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Update Data</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#pesticidesDataTable').DataTable({
        responsive: true,
        order: [[0, 'desc']]
    });

    // Form submission handler for add form
    $('#addPesticidesForm').submit(function(e) {
        e.preventDefault();
        submitForm($(this), 'add');
    });

    // Form submission handler for edit form
    $('#editPesticidesForm').submit(function(e) {
        e.preventDefault();
        submitForm($(this), 'update');
    });

    function submitForm($form, action) {
        const submitBtn = $form.find('button[type="submit"]');
        const originalText = submitBtn.html();
        const loadingText = action === 'add' ? 
            '<i class="fas fa-spinner fa-spin"></i> Saving...' : 
            '<i class="fas fa-spinner fa-spin"></i> Updating...';

        $.ajax({
            url: $form.attr('action'),
            type: 'POST',
            data: $form.serialize(),
            dataType: 'json',
            beforeSend: function() {
                submitBtn.prop('disabled', true).html(loadingText);
            },
            success: function(response) {
                if (response.status === 'success') {
                    toastr.success(response.message);
                    if (action === 'add') {
                        $form[0].reset();
                        $('#addPesticidesDataModal').modal('hide');
                    } else {
                        $('#editPesticidesDataModal').modal('hide');
                    }
                    setTimeout(() => location.reload(), 1500);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error(error);
                toastr.error('An error occurred while processing your request');
            },
            complete: function() {
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    }

    // Handle edit button click
    $('.edit-btn').click(function() {
        const data = $(this).data();
        
        // Populate form fields
        $('#edit_id').val(data.id);
        $('#edit_pesticide_type_id').val(data.pesticide_type_id);
        $('#edit_name').val(data.name);
        $('#edit_brand').val(data.brand);
        $('#edit_unit').val(data.unit);
        $('#edit_quantity').val(data.quantity);
        $('#edit_unit_of_measure').val(data.unit_of_measure);
        $('#edit_action_date').val(data.action_date);
        $('#edit_remarks').val(data.remarks);
    });
});
</script>
<?= $this->endSection() ?>