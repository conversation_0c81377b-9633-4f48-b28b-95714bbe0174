<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0">Livestock Farm Blocks</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="farmBlocksTable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Block Code</th>
                        <th>Farmer</th>
                        <th>Location</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($farm_blocks as $index => $block): ?>
                        <tr>
                            <td><?= $index + 1 ?></td>
                            <td><?= esc($block['block_code']) ?></td>
                            <td><?= esc($block['given_name'] . ' ' . $block['surname']) ?></td>
                            <td>
                                <?= esc($block['village']) ?> - <?= esc($block['block_site']) ?><br>
                                <?= esc($block['ward_name']) ?>, <?= esc($block['llg_name']) ?><br>
                                <?= esc($block['district_name']) ?>, <?= esc($block['province_name']) ?>
                            </td>
                            <td>
                                <?php if ($block['status'] == 'active'): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="<?= base_url('staff/livestock/view-farm-data/' . $block['id']) ?>" 
                                   class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye"></i> View Livestock Data
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#farmBlocksTable').DataTable({
        "responsive": true,
        "processing": true,
        "pageLength": 10,
        "language": {
            "lengthMenu": "Show _MENU_ entries",
            "zeroRecords": "No farm blocks found",
            "info": "Showing _START_ to _END_ of _TOTAL_ farm blocks",
            "infoEmpty": "Showing 0 to 0 of 0 farm blocks",
            "infoFiltered": "(filtered from _MAX_ total farm blocks)"
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 