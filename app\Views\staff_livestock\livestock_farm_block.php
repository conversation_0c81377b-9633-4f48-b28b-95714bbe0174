<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<?php if ($farm_blocks): ?>
<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0 float-start">Livestock Farm Blocks</h5>
        <button type="button" class="btn btn-success float-end" data-bs-toggle="modal" data-bs-target="#addFarmBlockModal">
            <i class="fas fa-plus"></i> Add Farm Block
        </button>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="farmBlocksTable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Block Code</th>
                        <th>Location</th>
                        <th>Coordinates</th>
                        <th>Farmer</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($farm_blocks as $index => $block): ?>
                        <tr class="align-middle">
                            <td><?= $index + 1 ?></td>
                            <td><?= esc($block['block_code']) ?></td>
                            <td><?= esc($block['village']) ?> - <?= esc($block['block_site']) ?></td>
                            <td>
                                Lat: <?= esc($block['lat']) ?><br>
                                Lon: <?= esc($block['lon']) ?>
                            </td>
                            <td>
                                <?= esc($block['given_name'] . ' ' . $block['surname']) ?>
                            </td>
                            <td><?= esc($block['status']) ?></td>
                            <td>
                                <button type="button" class="btn btn-primary btn-sm edit-block" 
                                        data-id="<?= $block['id'] ?>"
                                        data-block-code="<?= $block['block_code'] ?>"
                                        data-village="<?= $block['village'] ?>"
                                        data-block-site="<?= $block['block_site'] ?>"
                                        data-farmer-id="<?= $block['farmer_id'] ?>"
                                        data-lat="<?= $block['lat'] ?>"
                                        data-lon="<?= $block['lon'] ?>"
                                        data-status="<?= $block['status'] ?>"
                                        data-remarks="<?= $block['remarks'] ?>"
                                        data-district-id="<?= $block['district_id'] ?>"
                                        data-llg-id="<?= $block['llg_id'] ?>"
                                        data-ward-id="<?= $block['ward_id'] ?>">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php else: ?>
<div class="card">
    <div class="card-body text-center text-muted">
        <p>No livestock farm blocks found.</p>
    </div>
</div>
<?php endif; ?>

<!-- Add Farm Block Modal -->
<div class="modal fade" id="addFarmBlockModal" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-plus-circle"></i> Add Farm Block</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?= form_open_multipart('staff/livestock/add-farm-block', ['id' => 'addFarmBlockForm']) ?>
            <div class="modal-body">
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-12 mb-3">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Basic Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="farmer_id" class="form-label">Farmer</label>
                                            <select name="farmer_id" id="farmer_id" class="form-select select2-farmers" required>
                                                <option value="">Select a farmer</option>
                                                <?php if (isset($farmers)):
                                                    foreach ($farmers as $farmer): ?>
                                                        <option value="<?= $farmer['id'] ?>">
                                                            <?= esc($farmer['farmer_code']) ?> - <?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?>
                                                        </option>
                                                <?php endforeach;
                                                endif; ?>
                                            </select>
                                        </div>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Location Information -->
                    <div class="col-md-12 mb-3">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Location Details</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="district_id" class="form-label">District</label>
                                            <select class="form-select" id="district_id" name="district_id" required>
                                                <option value="">Select District</option>
                                                <?php foreach ($districts as $district): ?>
                                                    <option value="<?= $district['id'] ?>"><?= esc($district['name']) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="llg_id" class="form-label">LLG</label>
                                            <select class="form-select" id="llg_id" name="llg_id" required>
                                                <option value="">Select LLG</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="ward_id" class="form-label">Ward</label>
                                            <select class="form-select" id="ward_id" name="ward_id" required>
                                                <option value="">Select Ward</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="village" class="form-label">Village</label>
                                            <input type="text" class="form-control" id="village" name="village" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="block_site" class="form-label">Block Site</label>
                                            <input type="text" class="form-control" id="block_site" name="block_site" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Details -->
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Additional Details</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="lat" class="form-label">Latitude</label>
                                            <input type="number" step="any" class="form-control" id="lat" name="lat" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="lon" class="form-label">Longitude</label>
                                            <input type="number" step="any" class="form-control" id="lon" name="lon" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="remarks" class="form-label">Remarks</label>
                                            <textarea class="form-control" id="remarks" name="remarks" rows="4"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="btnAddFarmBlock">
                    <i class="fas fa-save"></i> Save Farm Block
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit Farm Block Modal -->
<div class="modal fade" id="editFarmBlockModal" tabindex="-1" role="dialog" aria-labelledby="editModelTitleId" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Edit Farm Block</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?= form_open_multipart('staff/livestock/update-farm-block', ['id' => 'editFarmBlockForm']) ?>
            <input type="hidden" name="block_id" id="edit_block_id">
            <div class="modal-body">
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-12 mb-3">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Basic Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_farmer_id" class="form-label">Farmer</label>
                                            <select name="farmer_id" id="edit_farmer_id" class="form-select select2-farmers" required>
                                                <option value="">Select a farmer</option>
                                                <?php foreach ($farmers as $farmer): ?>
                                                    <option value="<?= $farmer['id'] ?>">
                                                        <?= esc($farmer['farmer_code']) ?> - <?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_block_code" class="form-label">Block Code</label>
                                            <input type="text" readonly class="form-control" id="edit_block_code" name="block_code" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Location Information -->
                    <div class="col-md-12 mb-3">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Location Details</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="edit_district_id" class="form-label">District</label>
                                            <select class="form-select" id="edit_district_id" name="district_id" required>
                                                <option value="">Select District</option>
                                                <?php foreach ($districts as $district): ?>
                                                    <option value="<?= $district['id'] ?>"><?= esc($district['name']) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="edit_llg_id" class="form-label">LLG</label>
                                            <select class="form-select" id="edit_llg_id" name="llg_id" required>
                                                <option value="">Select LLG</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="edit_ward_id" class="form-label">Ward</label>
                                            <select class="form-select" id="edit_ward_id" name="ward_id" required>
                                                <option value="">Select Ward</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_village" class="form-label">Village</label>
                                            <input type="text" class="form-control" id="edit_village" name="village" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_block_site" class="form-label">Block Site</label>
                                            <input type="text" class="form-control" id="edit_block_site" name="block_site" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Details -->
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Additional Details</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_lat" class="form-label">Latitude</label>
                                            <input type="number" step="any" class="form-control" id="edit_lat" name="lat" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="edit_lon" class="form-label">Longitude</label>
                                            <input type="number" step="any" class="form-control" id="edit_lon" name="lon" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_status" class="form-label">Status</label>
                                            <select class="form-select" id="edit_status" name="status" required>
                                                <option value="active">Active</option>
                                                <option value="inactive">Inactive</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="edit_remarks" class="form-label">Remarks</label>
                                            <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="btnUpdateFarmBlock">
                    <i class="fas fa-save"></i> Update Farm Block
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize Select2 for farmer selections
    $('.select2-farmers').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select a farmer',
        allowClear: true,
        dropdownParent: $('#addFarmBlockModal')
    });

    $('#edit_farmer_id').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select a farmer',
        allowClear: true,
        dropdownParent: $('#editFarmBlockModal')
    });

    // Initialize DataTable
    const table = $('#farmBlocksTable').DataTable({
        "responsive": true,
        "processing": true,
        "pageLength": 10,
        "language": {
            "lengthMenu": "Show _MENU_ entries",
            "zeroRecords": "No farm blocks found",
            "info": "Showing _START_ to _END_ of _TOTAL_ farm blocks",
            "infoEmpty": "Showing 0 to 0 of 0 farm blocks",
            "infoFiltered": "(filtered from _MAX_ total farm blocks)"
        },
        "columnDefs": [{
            "orderable": false,
            "targets": -1
        }]
    });

    // Load LLGs when district is selected (for add form)
    $('#district_id').on('change', function() {
        const districtId = $(this).val();
        if (districtId) {
            $.ajax({
                url: '<?= base_url('staff/livestock/get-llgs') ?>',
                type: 'POST',
                data: { district_id: districtId },
                beforeSend: function() {
                    $('#llg_id').html('<option value="">Loading...</option>');
                    $('#ward_id').html('<option value="">Select Ward</option>');
                },
                success: function(response) {
                    if (response.success) {
                        let options = '<option value="">Select LLG</option>';
                        response.llgs.forEach(function(llg) {
                            options += `<option value="${llg.id}">${llg.name}</option>`;
                        });
                        $('#llg_id').html(options);
                    } else {
                        toastr.error('Failed to load LLGs');
                    }
                },
                error: function() {
                    toastr.error('Failed to load LLGs');
                    $('#llg_id').html('<option value="">Select LLG</option>');
                }
            });
        } else {
            $('#llg_id').html('<option value="">Select LLG</option>');
            $('#ward_id').html('<option value="">Select Ward</option>');
        }
    });

    // Load Wards when LLG is selected
    $('#llg_id').on('change', function() {
        const llgId = $(this).val();
        if (llgId) {
            $.ajax({
                url: '<?= base_url('staff/livestock/get-wards') ?>',
                type: 'POST',
                data: { llg_id: llgId },
                beforeSend: function() {
                    $('#ward_id').html('<option value="">Loading...</option>');
                },
                success: function(response) {
                    if (response.success) {
                        let options = '<option value="">Select Ward</option>';
                        response.wards.forEach(function(ward) {
                            options += `<option value="${ward.id}">${ward.name}</option>`;
                        });
                        $('#ward_id').html(options);
                    } else {
                        toastr.error('Failed to load Wards');
                    }
                },
                error: function() {
                    toastr.error('Failed to load Wards');
                    $('#ward_id').html('<option value="">Select Ward</option>');
                }
            });
        } else {
            $('#ward_id').html('<option value="">Select Ward</option>');
        }
    });

    // Add Farm Block
    $('#btnAddFarmBlock').on('click', function() {
        var formData = new FormData($('#addFarmBlockForm')[0]);
        $.ajax({
            url: "<?= base_url('staff/livestock/add-farm-block') ?>",
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            beforeSend: function() {
                $('#btnAddFarmBlock').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Saving...');
            },
            success: function(response) {
                if (response.status === 'success') {
                    toastr.success(response.message);
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', xhr.responseText);
                toastr.error('An error occurred while saving the farm block');
            },
            complete: function() {
                $('#btnAddFarmBlock').prop('disabled', false).html('<i class="fas fa-save"></i> Save Farm Block');
            }
        });
    });

    // Edit Farm Block
    $('.edit-block').on('click', async function() {
        const data = $(this).data();
        $('#edit_block_id').val(data.id);
        $('#edit_block_code').val(data.blockCode);
        $('#edit_farmer_id').val(data.farmerId).trigger('change');
        $('#edit_village').val(data.village);
        $('#edit_block_site').val(data.blockSite);
        $('#edit_lat').val(data.lat);
        $('#edit_lon').val(data.lon);
        $('#edit_status').val(data.status);
        $('#edit_remarks').val(data.remarks);

        // Set district and load dependent dropdowns
        $('#edit_district_id').val(data.districtId);
        await loadLocationData(data);

        $('#editFarmBlockModal').modal('show');
    });

    // Update Farm Block
    $('#btnUpdateFarmBlock').on('click', function() {
        var formData = new FormData($('#editFarmBlockForm')[0]);
        $.ajax({
            url: "<?= base_url('staff/livestock/update-farm-block') ?>",
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            beforeSend: function() {
                $('#btnUpdateFarmBlock').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Updating...');
            },
            success: function(response) {
                if (response.status === 'success') {
                    toastr.success(response.message);
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', xhr.responseText);
                toastr.error('An error occurred while updating the farm block');
            },
            complete: function() {
                $('#btnUpdateFarmBlock').prop('disabled', false).html('<i class="fas fa-save"></i> Update Farm Block');
            }
        });
    });

    // Load location data for edit form
    async function loadLocationData(data) {
        try {
            // Load LLGs
            const llgResponse = await $.ajax({
                url: '<?= base_url('staff/livestock/get-llgs') ?>',
                type: 'POST',
                data: { district_id: data.districtId }
            });

            if (llgResponse.success) {
                let llgOptions = '<option value="">Select LLG</option>';
                llgResponse.llgs.forEach(function(llg) {
                    const selected = llg.id == data.llgId ? 'selected' : '';
                    llgOptions += `<option value="${llg.id}" ${selected}>${llg.name}</option>`;
                });
                $('#edit_llg_id').html(llgOptions);

                // Load Wards
                const wardResponse = await $.ajax({
                    url: '<?= base_url('staff/livestock/get-wards') ?>',
                    type: 'POST',
                    data: { llg_id: data.llgId }
                });

                if (wardResponse.success) {
                    let wardOptions = '<option value="">Select Ward</option>';
                    wardResponse.wards.forEach(function(ward) {
                        const selected = ward.id == data.wardId ? 'selected' : '';
                        wardOptions += `<option value="${ward.id}" ${selected}>${ward.name}</option>`;
                    });
                    $('#edit_ward_id').html(wardOptions);
                }
            }
        } catch (error) {
            console.error('Error loading location data:', error);
            toastr.error('Failed to load location data');
        }
    }

    // Handle district change in edit modal
    $('#edit_district_id').on('change', function() {
        const districtId = $(this).val();
        if (districtId) {
            $.ajax({
                url: '<?= base_url('staff/livestock/get-llgs') ?>',
                type: 'POST',
                data: { district_id: districtId },
                beforeSend: function() {
                    $('#edit_llg_id').html('<option value="">Loading...</option>');
                    $('#edit_ward_id').html('<option value="">Select Ward</option>');
                },
                success: function(response) {
                    if (response.success) {
                        let options = '<option value="">Select LLG</option>';
                        response.llgs.forEach(function(llg) {
                            options += `<option value="${llg.id}">${llg.name}</option>`;
                        });
                        $('#edit_llg_id').html(options);
                    } else {
                        toastr.error('Failed to load LLGs');
                    }
                },
                error: function() {
                    toastr.error('Failed to load LLGs');
                    $('#edit_llg_id').html('<option value="">Select LLG</option>');
                }
            });
        } else {
            $('#edit_llg_id').html('<option value="">Select LLG</option>');
            $('#edit_ward_id').html('<option value="">Select Ward</option>');
        }
    });

    // Handle LLG change in edit modal
    $('#edit_llg_id').on('change', function() {
        const llgId = $(this).val();
        if (llgId) {
            $.ajax({
                url: '<?= base_url('staff/livestock/get-wards') ?>',
                type: 'POST',
                data: { llg_id: llgId },
                beforeSend: function() {
                    $('#edit_ward_id').html('<option value="">Loading...</option>');
                },
                success: function(response) {
                    if (response.success) {
                        let options = '<option value="">Select Ward</option>';
                        response.wards.forEach(function(ward) {
                            options += `<option value="${ward.id}">${ward.name}</option>`;
                        });
                        $('#edit_ward_id').html(options);
                    } else {
                        toastr.error('Failed to load Wards');
                    }
                },
                error: function() {
                    toastr.error('Failed to load Wards');
                    $('#edit_ward_id').html('<option value="">Select Ward</option>');
                }
            });
        } else {
            $('#edit_ward_id').html('<option value="">Select Ward</option>');
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 