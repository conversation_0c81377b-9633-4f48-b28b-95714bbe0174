<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('staff/dashboard') ?>">Dashboard</a></li>
            <li class="breadcrumb-item">Reports</li>
            <li class="breadcrumb-item active" aria-current="page">Crops Report</li>
        </ol>
    </nav>

    <!-- Charts Row -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Crops Distribution</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="cropsDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Total Plants by Crop</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="plantsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Total Hectares by Crop</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="hectaresChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Crops Distribution by LLG</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="llgDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- LLG Distribution Summary Card -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Distribution by LLG</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered text-nowrap">
                    <thead>
                        <tr>
                            <th>LLG</th>
                            <?php 
                            // Get unique crop names
                            $unique_crops = array_unique(array_map(function($item) {
                                return $item['crop_name'];
                            }, $crops_data));
                            sort($unique_crops);
                            
                            foreach ($unique_crops as $crop_name): ?>
                                <th class="text-center" colspan="2"><?= esc($crop_name) ?></th>
                            <?php endforeach; ?>
                            <th class="text-center" colspan="2">Total</th>
                        </tr>
                        <tr>
                            <th></th>
                            <?php foreach ($unique_crops as $crop_name): ?>
                                <th class="text-center">Blocks</th>
                                <th class="text-center">Hectares</th>
                            <?php endforeach; ?>
                            <th class="text-center">Blocks</th>
                            <th class="text-center">Hectares</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Get unique LLGs and sort them
                        $unique_llgs = array_unique(array_map(function($item) {
                            return $item['llg_name'];
                        }, $crops_data));
                        sort($unique_llgs);

                        // Initialize totals
                        $crop_totals = array_fill_keys($unique_crops, ['blocks' => 0, 'hectares' => 0]);
                        $grand_total = ['blocks' => 0, 'hectares' => 0];

                        // Group data by LLG and Crop
                        $llg_data = [];
                        foreach ($crops_data as $crop) {
                            $llg = $crop['llg_name'];
                            $crop_name = $crop['crop_name'];
                            
                            if (!isset($llg_data[$llg])) {
                                $llg_data[$llg] = array_fill_keys($unique_crops, ['blocks' => 0, 'hectares' => 0]);
                            }
                            
                            $llg_data[$llg][$crop_name]['blocks']++;
                            $llg_data[$llg][$crop_name]['hectares'] += 
                                (float)($crop['total_hectares_added'] ?? 0) - (float)($crop['total_hectares_removed'] ?? 0);
                        }

                        // Display data
                        foreach ($unique_llgs as $llg):
                            $llg_total = ['blocks' => 0, 'hectares' => 0];
                        ?>
                            <tr>
                                <td><?= esc($llg) ?></td>
                                <?php foreach ($unique_crops as $crop_name): 
                                    $blocks = $llg_data[$llg][$crop_name]['blocks'] ?? 0;
                                    $hectares = $llg_data[$llg][$crop_name]['hectares'] ?? 0;
                                    
                                    // Update totals
                                    $crop_totals[$crop_name]['blocks'] += $blocks;
                                    $crop_totals[$crop_name]['hectares'] += $hectares;
                                    $llg_total['blocks'] += $blocks;
                                    $llg_total['hectares'] += $hectares;
                                ?>
                                    <td class="text-end"><?= $blocks ? number_format($blocks) : '-' ?></td>
                                    <td class="text-end"><?= $hectares ? number_format($hectares, 2) : '-' ?></td>
                                <?php endforeach; ?>
                                <td class="text-end fw-bold"><?= number_format($llg_total['blocks']) ?></td>
                                <td class="text-end fw-bold"><?= number_format($llg_total['hectares'], 2) ?></td>
                            </tr>
                        <?php 
                            $grand_total['blocks'] += $llg_total['blocks'];
                            $grand_total['hectares'] += $llg_total['hectares'];
                        endforeach; 
                        ?>
                        <tr class="table-light">
                            <td class="fw-bold">Total</td>
                            <?php foreach ($unique_crops as $crop_name): ?>
                                <td class="text-end fw-bold"><?= number_format($crop_totals[$crop_name]['blocks']) ?></td>
                                <td class="text-end fw-bold"><?= number_format($crop_totals[$crop_name]['hectares'], 2) ?></td>
                            <?php endforeach; ?>
                            <td class="text-end fw-bold"><?= number_format($grand_total['blocks']) ?></td>
                            <td class="text-end fw-bold"><?= number_format($grand_total['hectares'], 2) ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Table Card -->
    <div class="card">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-seedling me-2"></i>Crops Report</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-bordered text-nowrap">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Block Code</th>
                            <th>Crop</th>
                            <th>Last Action Date</th>
                            <th>Total Plants Added</th>
                            <th>Total Plants Removed</th>
                            <th>Available Plants</th>
                            <th>Total Hectares Added</th>
                            <th>Total Hectares Removed</th>
                            <th>Available Hectares</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $loop_count = 1;
                        foreach ($crops_data as $crop): 
                            $available_plants = ($crop['total_plants_added'] ?? 0) - ($crop['total_plants_removed'] ?? 0);
                            $available_hectares = ($crop['total_hectares_added'] ?? 0) - ($crop['total_hectares_removed'] ?? 0);
                        ?>
                            <tr>
                                <td><?= $loop_count++ ?></td>
                                <td><?= esc($crop['block_code']) ?></td>
                                <td><?= esc($crop['crop_name']) ?></td>
                                <td>
                                    <?php 
                                    $action_date = strtotime($crop['latest_action_date']);
                                    $is_recent = (time() - $action_date) < (7 * 24 * 60 * 60); // Within last 7 days
                                    ?>
                                    <?= date('d M Y', $action_date) ?>
                                    <?php if ($is_recent): ?>
                                        <span class="badge bg-success ms-1" title="Recent activity">New</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-end"><?= number_format($crop['total_plants_added'] ?? 0) ?></td>
                                <td class="text-end"><?= number_format($crop['total_plants_removed'] ?? 0) ?></td>
                                <td class="text-end <?= $available_plants < 0 ? 'text-danger' : '' ?>">
                                    <?= number_format($available_plants) ?>
                                </td>
                                <td class="text-end"><?= number_format($crop['total_hectares_added'] ?? 0, 2) ?></td>
                                <td class="text-end"><?= number_format($crop['total_hectares_removed'] ?? 0, 2) ?></td>
                                <td class="text-end <?= $available_hectares < 0 ? 'text-danger' : '' ?>">
                                    <?= number_format($available_hectares, 2) ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $crop['status'] === 'active' ? 'success' : 'danger' ?>">
                                        <?= ucfirst($crop['status']) ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Chart data
    const cropsData = <?= json_encode($crops_data) ?>;
    
    // Chart configurations
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    boxWidth: 12,
                    padding: 15
                }
            }
        }
    };

    // Process data for charts
    const cropStats = cropsData.reduce((acc, crop) => {
        if (!acc[crop.crop_name]) {
            acc[crop.crop_name] = {
                count: 0,
                plants: 0,
                hectares: 0,
                color: crop.crop_color_code || '#' + Math.floor(Math.random()*16777215).toString(16)
            };
        }
        acc[crop.crop_name].count++;
        acc[crop.crop_name].plants += parseInt(crop.total_plants_added || 0) - parseInt(crop.total_plants_removed || 0);
        acc[crop.crop_name].hectares += parseFloat(crop.total_hectares_added || 0) - parseFloat(crop.total_hectares_removed || 0);
        return acc;
    }, {});

    // Process data for LLG distribution
    const llgStats = cropsData.reduce((acc, crop) => {
        const llgName = crop.llg_name || 'Unknown';
        if (!acc[llgName]) {
            acc[llgName] = {
                crops: {}
            };
        }
        if (!acc[llgName].crops[crop.crop_name]) {
            acc[llgName].crops[crop.crop_name] = {
                count: 0,
                hectares: 0,
                color: crop.crop_color_code
            };
        }
        acc[llgName].crops[crop.crop_name].count++;
        acc[llgName].crops[crop.crop_name].hectares += 
            (parseFloat(crop.total_hectares_added || 0) - parseFloat(crop.total_hectares_removed || 0));
        return acc;
    }, {});

    // Crops Distribution Chart
    new Chart(document.getElementById('cropsDistributionChart'), {
        type: 'pie',
        data: {
            labels: Object.keys(cropStats),
            datasets: [{
                data: Object.values(cropStats).map(stat => stat.count),
                backgroundColor: Object.values(cropStats).map(stat => stat.color)
            }]
        },
        options: chartOptions
    });

    // Plants Chart
    new Chart(document.getElementById('plantsChart'), {
        type: 'bar',
        data: {
            labels: Object.keys(cropStats),
            datasets: [{
                label: 'Number of Plants',
                data: Object.values(cropStats).map(stat => stat.plants),
                backgroundColor: Object.values(cropStats).map(stat => stat.color)
            }]
        },
        options: {
            ...chartOptions,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Hectares Chart
    new Chart(document.getElementById('hectaresChart'), {
        type: 'bar',
        data: {
            labels: Object.keys(cropStats),
            datasets: [{
                label: 'Hectares',
                data: Object.values(cropStats).map(stat => stat.hectares),
                backgroundColor: Object.values(cropStats).map(stat => stat.color)
            }]
        },
        options: {
            ...chartOptions,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2);
                        }
                    }
                }
            }
        }
    });

    // LLG Distribution Chart
    const llgLabels = Object.keys(llgStats);
    const cropTypes = [...new Set(cropsData.map(d => d.crop_name))];
    const llgDatasets = cropTypes.map(cropName => ({
        label: cropName,
        data: llgLabels.map(llg => llgStats[llg].crops[cropName]?.hectares || 0),
        backgroundColor: cropsData.find(d => d.crop_name === cropName)?.crop_color_code || '#000000'
    }));

    new Chart(document.getElementById('llgDistributionChart'), {
        type: 'bar',
        data: {
            labels: llgLabels,
            datasets: llgDatasets
        },
        options: {
            ...chartOptions,
            scales: {
                x: {
                    stacked: true
                },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2) + ' ha';
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${context.parsed.y.toFixed(2)} ha`;
                        }
                    }
                }
            }
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
