<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('staff/dashboard') ?>">Dashboard</a></li>
            <li class="breadcrumb-item">Reports</li>
            <li class="breadcrumb-item active" aria-current="page">Livestock Data Report</li>
        </ol>
    </nav>

    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800"><?= $page_header ?></h1>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Total Records Card -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Records</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['total_records'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Male Card -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Male</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['total_male'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-mars fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Female Card -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Female</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $stats['total_female'] ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-venus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Distribution by Livestock Type</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="livestockTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Gender Distribution</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="genderChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-4">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">Distribution by LLG</h6>
                    <div class="chart-container" style="position: relative; height: 250px;">
                        <canvas id="llgDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Trend Chart -->
    <div class="card mb-4">
        <div class="card-body">
            <h6 class="card-title">Monthly Livestock Count Trend (Last 12 Months)</h6>
            <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="trendChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Cost Analysis by LLG -->
    <div class="row mb-4">
        <div class="col-xl-12">
            <div class="card shadow">
                <div class="card-header py-3 bg-white">
                    <h6 class="m-0 font-weight-bold text-primary">Cost and Price Analysis by LLG</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="thead-light">
                                <tr>
                                    <th>LLG</th>
                                    <th>Livestock Type</th>
                                    <th>Total Count</th>
                                    <th>Avg Cost/Unit</th>
                                    <th>Cost Range</th>
                                    <th>Avg Price Range</th>
                                    <th>Price Range (Min-Max)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($stats['by_llg'] as $llg => $llg_data): ?>
                                    <?php foreach ($llg_data['by_livestock_type'] as $type => $data): ?>
                                    <tr>
                                        <td><?= $llg ?></td>
                                        <td><?= $type ?></td>
                                        <td><?= $data['total'] ?></td>
                                        <td>K<?= number_format($data['total_cost'] / $data['count'], 2) ?></td>
                                        <td>K<?= number_format($data['min_cost'], 2) ?> - K<?= number_format($data['max_cost'], 2) ?></td>
                                        <td>K<?= number_format($data['total_low_price'] / $data['count'], 2) ?> - K<?= number_format($data['total_high_price'] / $data['count'], 2) ?></td>
                                        <td>K<?= number_format($data['min_low_price'], 2) ?> - K<?= number_format($data['max_high_price'], 2) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <tr class="table-secondary">
                                        <td colspan="2"><strong><?= $llg ?> Total</strong></td>
                                        <td><strong><?= $llg_data['total'] ?></strong></td>
                                        <td><strong>K<?= number_format($llg_data['total_cost'] / $llg_data['count'], 2) ?></strong></td>
                                        <td><strong>K<?= number_format($llg_data['min_cost'], 2) ?> - K<?= number_format($llg_data['max_cost'], 2) ?></strong></td>
                                        <td><strong>K<?= number_format($llg_data['total_low_price'] / $llg_data['count'], 2) ?> - K<?= number_format($llg_data['total_high_price'] / $llg_data['count'], 2) ?></strong></td>
                                        <td><strong>K<?= number_format($llg_data['min_low_price'], 2) ?> - K<?= number_format($llg_data['max_high_price'], 2) ?></strong></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-white d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Livestock Data</h6>
            <div class="btn-group">
                <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print fa-sm mr-1"></i> Print
                </button>
            </div>
        </div>
        <div class="card-body table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="thead-light">
                    <tr>
                        <th>Block Code</th>
                        <th>Farmer Name</th>
                        <th>Livestock Type</th>
                        <th>Breed</th>
                        <th>Male</th>
                        <th>Female</th>
                        <th>Total</th>
                        <th>Growth Stage</th>
                        <th>LLG</th>
                        <th>Date</th>
                        <th>Cost/Unit</th>
                        <th>Price Range</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($livestock_data as $data): ?>
                    <tr>
                        <td><?= esc($data['block_code']) ?></td>
                        <td><?= esc($data['given_name']) . ' ' . esc($data['surname']) ?></td>
                        <td><?= esc($data['livestock_name']) ?></td>
                        <td><?= esc($data['breed']) ?></td>
                        <td><?= $data['he_total'] ?></td>
                        <td><?= $data['she_total'] ?></td>
                        <td><?= $data['he_total'] + $data['she_total'] ?></td>
                        <td><?= esc($data['growth_stage']) ?></td>
                        <td><?= esc($data['llg_name']) ?></td>
                        <td><?= dateforms($data['action_date']) ?></td>
                        <td>K<?= number_format($data['cost_per_livestock'], 2) ?></td>
                        <td>K<?= number_format($data['low_price_per_livestock'], 2) ?> - K<?= number_format($data['high_price_per_livestock'], 2) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    const stats = <?= json_encode($stats) ?>;
    const livestock_data = <?= json_encode($livestock_data) ?>;
    
    // Chart configurations
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    boxWidth: 12,
                    padding: 15
                }
            }
        }
    };

    // Process data for charts
    const livestockTypeStats = livestock_data.reduce((acc, record) => {
        if (!acc[record.livestock_name]) {
            acc[record.livestock_name] = {
                count: 0,
                male: 0,
                female: 0,
                total: 0
            };
        }
        acc[record.livestock_name].count++;
        acc[record.livestock_name].male += parseInt(record.he_total);
        acc[record.livestock_name].female += parseInt(record.she_total);
        acc[record.livestock_name].total += parseInt(record.he_total) + parseInt(record.she_total);
        return acc;
    }, {});

    // Process data for LLG distribution
    const llgStats = livestock_data.reduce((acc, record) => {
        const llgName = record.llg_name || 'Unknown';
        if (!acc[llgName]) {
            acc[llgName] = {
                livestock: {},
                total: 0
            };
        }
        if (!acc[llgName].livestock[record.livestock_name]) {
            acc[llgName].livestock[record.livestock_name] = 0;
        }
        acc[llgName].livestock[record.livestock_name] += parseInt(record.he_total) + parseInt(record.she_total);
        acc[llgName].total += parseInt(record.he_total) + parseInt(record.she_total);
        return acc;
    }, {});

    // Livestock Type Chart
    new Chart(document.getElementById('livestockTypeChart'), {
        type: 'pie',
        data: {
            labels: Object.keys(livestockTypeStats),
            datasets: [{
                data: Object.values(livestockTypeStats).map(stat => stat.total),
                backgroundColor: [
                    '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                    '#858796', '#5a5c69', '#2e59d9', '#17a673', '#2c9faf'
                ]
            }]
        },
        options: {
            ...chartOptions,
            plugins: {
                ...chartOptions.plugins,
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });

    // Gender Distribution Chart
    new Chart(document.getElementById('genderChart'), {
        type: 'bar',
        data: {
            labels: Object.keys(livestockTypeStats),
            datasets: [
                {
                    label: 'Male',
                    data: Object.values(livestockTypeStats).map(stat => stat.male),
                    backgroundColor: '#4e73df'
                },
                {
                    label: 'Female',
                    data: Object.values(livestockTypeStats).map(stat => stat.female),
                    backgroundColor: '#e74a3b'
                }
            ]
        },
        options: {
            ...chartOptions,
            scales: {
                x: {
                    stacked: true
                },
                y: {
                    stacked: true,
                    beginAtZero: true
                }
            }
        }
    });

    // LLG Distribution Chart
    new Chart(document.getElementById('llgDistributionChart'), {
        type: 'pie',
        data: {
            labels: Object.keys(llgStats),
            datasets: [{
                data: Object.values(llgStats).map(stat => stat.total),
                backgroundColor: [
                    '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                    '#858796', '#5a5c69', '#2e59d9', '#17a673', '#2c9faf'
                ]
            }]
        },
        options: chartOptions
    });

    // Monthly Trend Chart
    const now = new Date();
    const monthlyStats = {};
    const months = [];
    
    // Initialize last 12 months
    for (let i = 11; i >= 0; i--) {
        const d = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthKey = d.toLocaleString('default', { year: 'numeric', month: 'short' });
        monthlyStats[monthKey] = { total: 0, male: 0, female: 0 };
        months.push(monthKey);
    }

    livestock_data.forEach(record => {
        const recordDate = new Date(record.action_date);
        const monthKey = recordDate.toLocaleString('default', { year: 'numeric', month: 'short' });
        if (monthlyStats[monthKey]) {
            monthlyStats[monthKey].total += parseInt(record.he_total) + parseInt(record.she_total);
            monthlyStats[monthKey].male += parseInt(record.he_total);
            monthlyStats[monthKey].female += parseInt(record.she_total);
        }
    });

    new Chart(document.getElementById('trendChart'), {
        type: 'line',
        data: {
            labels: months,
            datasets: [
                {
                    label: 'Total',
                    data: months.map(month => monthlyStats[month].total),
                    borderColor: '#4e73df',
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    fill: true
                },
                {
                    label: 'Male',
                    data: months.map(month => monthlyStats[month].male),
                    borderColor: '#1cc88a',
                    backgroundColor: 'rgba(28, 200, 138, 0.1)',
                    fill: true
                },
                {
                    label: 'Female',
                    data: months.map(month => monthlyStats[month].female),
                    borderColor: '#e74a3b',
                    backgroundColor: 'rgba(231, 74, 59, 0.1)',
                    fill: true
                }
            ]
        },
        options: {
            ...chartOptions,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>

<style>
    .table thead th {
        background-color: #f8f9fc;
        border-bottom: 2px solid #e3e6f0;
    }
    .table-hover tbody tr:hover {
        background-color: #f8f9fc;
    }
    .chart-container {
        position: relative;
        margin: auto;
    }
</style>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 