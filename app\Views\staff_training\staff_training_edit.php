<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="card-title mb-0">Edit Training</h4>
                    </div>
                    <div class="col-auto">
                        <a href="<?= base_url('staff/extension/trainings') ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?= form_open('staff/extension/trainings/update/' . $training['id']) ?>
                
                    <?php 
                        // Parse JSON fields
                        $locations = json_decode($training['locations'], true);
                        $gps = json_decode($training['gps'], true);
                        $content = json_decode($training['content'], true);
                        $materials = json_decode($training['materials'], true);
                    ?>
                
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="topic" class="form-label">Training Topic <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="topic" name="topic" 
                                value="<?= old('topic', $training['topic']) ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="">Select Status</option>
                                <option value="1" <?= old('status', $training['status']) == '1' ? 'selected' : '' ?>>Completed</option>
                                <option value="2" <?= old('status', $training['status']) == '2' ? 'selected' : '' ?>>Ongoing</option>
                                <option value="3" <?= old('status', $training['status']) == '3' ? 'selected' : '' ?>>Scheduled</option>
                                <option value="4" <?= old('status', $training['status']) == '4' ? 'selected' : '' ?>>Cancelled</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="date_start" class="form-label">Start Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date_start" name="date_start" 
                                value="<?= old('date_start', date('Y-m-d', strtotime($training['date_start']))) ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label for="date_end" class="form-label">End Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date_end" name="date_end" 
                                value="<?= old('date_end', date('Y-m-d', strtotime($training['date_end']))) ?>" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="venue" class="form-label">Venue <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="venue" name="venue" 
                                value="<?= old('venue', $locations['venue'] ?? '') ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" 
                                value="<?= old('location', $locations['location'] ?? '') ?>" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="latitude" class="form-label">Latitude</label>
                            <input type="text" class="form-control" id="latitude" name="latitude" 
                                value="<?= old('latitude', $gps['latitude'] ?? '') ?>">
                        </div>
                        <div class="col-md-6">
                            <label for="longitude" class="form-label">Longitude</label>
                            <input type="text" class="form-control" id="longitude" name="longitude" 
                                value="<?= old('longitude', $gps['longitude'] ?? '') ?>">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="objectives" class="form-label">Training Objectives <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="objectives" name="objectives" rows="3" required><?= old('objectives', $training['objectives']) ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="modules" class="form-label">Training Modules</label>
                        <textarea class="form-control" id="modules" name="modules" rows="3"><?= old('modules', $content['modules'] ?? '') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="activities" class="form-label">Training Activities</label>
                        <textarea class="form-control" id="activities" name="activities" rows="3"><?= old('activities', $content['activities'] ?? '') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="materials" class="form-label">Training Materials</label>
                        <textarea class="form-control" id="materials" name="materials" rows="3"><?= old('materials', $materials['materials'] ?? '') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Additional Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"><?= old('notes', $content['notes'] ?? '') ?></textarea>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="<?= base_url('staff/extension/trainings') ?>" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Update Training</button>
                    </div>
                    
                <?= form_close() ?>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Date validation - End date must be after start date
        $('#date_start, #date_end').on('change', function() {
            var startDate = $('#date_start').val();
            var endDate = $('#date_end').val();
            
            if (startDate && endDate && startDate > endDate) {
                toastr.error('End date cannot be before start date');
                $('#date_end').val('');
            }
        });
    });
</script>
<?= $this->endSection() ?> 