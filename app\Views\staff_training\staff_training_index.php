<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">Search & Filter</h5>
            </div>
            <div class="card-body">
                <form id="filterForm" class="row g-3">
                    <div class="col-md-4">
                        <label for="searchTopic" class="form-label">Topic</label>
                        <input type="text" class="form-control" id="searchTopic" placeholder="Search by topic">
                    </div>
                    <div class="col-md-3">
                        <label for="searchStatus" class="form-label">Status</label>
                        <select class="form-select" id="searchStatus">
                            <option value="">All Statuses</option>
                            <option value="1">Completed</option>
                            <option value="2">Ongoing</option>
                            <option value="3">Scheduled</option>
                            <option value="4">Cancelled</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="searchDate" class="form-label">Date Range</label>
                        <select class="form-select" id="searchDate">
                            <option value="">All Time</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="quarter">Last 3 Months</option>
                            <option value="year">This Year</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="button" id="resetFilters" class="btn btn-secondary w-100">
                            <i class="fas fa-redo"></i> Reset
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="card-title mb-0">Training Management</h4>
                    </div>
                    <div class="col-auto">
                        <a href="<?= base_url('staff/extension/trainings/dashboard') ?>" class="btn btn-primary me-2">
                            <i class="fas fa-chart-bar"></i> Dashboard
                        </a>
                        <a href="<?= base_url('staff/extension/trainings/export') ?>" class="btn btn-info me-2">
                            <i class="fas fa-file-export"></i> Export to CSV
                        </a>
                        <a href="<?= base_url('staff/extension/trainings/new') ?>" class="btn btn-success">
                            <i class="fas fa-plus-circle"></i> Add New Training
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="trainingsTable" class="table table-striped table-bordered dt-responsive nowrap">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Topic</th>
                                <th>Location</th>
                                <th>Date Start</th>
                                <th>Date End</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($trainings as $training): ?>
                                <?php 
                                    $locations = json_decode($training['locations'], true);
                                    $venue = $locations['venue'] ?? 'N/A';
                                    $location = $locations['location'] ?? 'N/A';
                                    
                                    $statusLabels = [
                                        '1' => '<span class="badge bg-success">Completed</span>',
                                        '2' => '<span class="badge bg-warning">Ongoing</span>',
                                        '3' => '<span class="badge bg-primary">Scheduled</span>',
                                        '4' => '<span class="badge bg-danger">Cancelled</span>'
                                    ];
                                    
                                    $statusLabel = $statusLabels[$training['status']] ?? '<span class="badge bg-secondary">Unknown</span>';
                                ?>
                                <tr id="training-row-<?= $training['id'] ?>">
                                    <td><?= $training['id'] ?></td>
                                    <td><?= esc($training['topic']) ?></td>
                                    <td><?= esc($venue) ?>, <?= esc($location) ?></td>
                                    <td><?= date('d-M-Y', strtotime($training['date_start'])) ?></td>
                                    <td><?= date('d-M-Y', strtotime($training['date_end'])) ?></td>
                                    <td><?= $statusLabel ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="<?= base_url('staff/extension/trainings/view/' . $training['id']) ?>" 
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('staff/extension/trainings/participants/' . $training['id']) ?>" 
                                               class="btn btn-sm btn-primary" title="Manage Participants">
                                                <i class="fas fa-users"></i>
                                            </a>
                                            <a href="<?= base_url('staff/extension/trainings/edit/' . $training['id']) ?>" 
                                               class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button"
                                               class="btn btn-sm btn-danger ajax-delete-btn"
                                               data-id="<?= $training['id'] ?>"
                                               data-title="<?= esc($training['topic']) ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the training: <span id="deleteItemTitle" class="fw-bold"></span>?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="deleteConfirmBtn" class="btn btn-danger">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast for showing AJAX messages -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="ajaxToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="toastTitle">Notification</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastMessage">
            Message here
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        var dataTable = $('#trainingsTable').DataTable({
            responsive: true,
            order: [[0, 'desc']]
        });
        
        // Filter functionality
        $('#searchTopic, #searchStatus, #searchDate').on('keyup change', function() {
            applyFilters();
        });
        
        // Reset filters button
        $('#resetFilters').on('click', function() {
            $('#filterForm')[0].reset();
            applyFilters();
        });
        
        // Apply filters to DataTable
        function applyFilters() {
            dataTable.search('').columns().search('').draw();
            
            const topic = $('#searchTopic').val().toLowerCase();
            const status = $('#searchStatus').val();
            const dateRange = $('#searchDate').val();
            
            // Custom filtering function
            $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                let rowTopic = data[1].toLowerCase();
                let rowStatus = extractStatusValue(data[5]);
                let rowStartDate = moment(data[3], 'DD-MMM-YYYY');
                
                // Topic filter
                if (topic && !rowTopic.includes(topic)) {
                    return false;
                }
                
                // Status filter
                if (status && rowStatus !== status) {
                    return false;
                }
                
                // Date range filter
                if (dateRange) {
                    const today = moment();
                    let startDate;
                    
                    switch(dateRange) {
                        case 'today':
                            if (!rowStartDate.isSame(today, 'day')) {
                                return false;
                            }
                            break;
                        case 'week':
                            startDate = moment().startOf('week');
                            if (!rowStartDate.isBetween(startDate, today, 'day', '[]')) {
                                return false;
                            }
                            break;
                        case 'month':
                            startDate = moment().startOf('month');
                            if (!rowStartDate.isBetween(startDate, today, 'day', '[]')) {
                                return false;
                            }
                            break;
                        case 'quarter':
                            startDate = moment().subtract(3, 'months');
                            if (!rowStartDate.isBetween(startDate, today, 'day', '[]')) {
                                return false;
                            }
                            break;
                        case 'year':
                            startDate = moment().startOf('year');
                            if (!rowStartDate.isBetween(startDate, today, 'day', '[]')) {
                                return false;
                            }
                            break;
                    }
                }
                
                return true;
            });
            
            dataTable.draw();
            
            // Clean up the custom filter to prevent stacking
            $.fn.dataTable.ext.search.pop();
        }
        
        // Extract status value from HTML badge
        function extractStatusValue(html) {
            if (html.includes('bg-success')) return '1';
            if (html.includes('bg-warning')) return '2';
            if (html.includes('bg-primary')) return '3';
            if (html.includes('bg-danger')) return '4';
            return '';
        }
        
        // Setup AJAX delete functionality
        let trainingId;
        let trainingTitle;
        let tableRow;
        
        $('.ajax-delete-btn').on('click', function(e) {
            e.preventDefault();
            trainingId = $(this).data('id');
            trainingTitle = $(this).data('title');
            tableRow = $('#training-row-' + trainingId);
            
            $('#deleteItemTitle').text(trainingTitle);
            $('#deleteModal').modal('show');
        });
        
        $('#deleteConfirmBtn').on('click', function(e) {
            e.preventDefault();
            
            // Send AJAX request to delete the training
            $.ajax({
                url: '<?= base_url('staff/extension/trainings/ajax-delete') ?>',
                type: 'POST',
                data: {
                    id: trainingId,
                    <?= csrf_token() ?>: '<?= csrf_hash() ?>'
                },
                dataType: 'json',
                success: function(response) {
                    // Close the modal
                    $('#deleteModal').modal('hide');
                    
                    if (response.success) {
                        // Remove the row from the table
                        dataTable.row(tableRow).remove().draw();
                        
                        // Show success message
                        $('#toastTitle').html('<i class="fas fa-check-circle text-success"></i> Success');
                        $('#toastMessage').text(response.message);
                        $('#ajaxToast').toast('show');
                    } else {
                        // Show error message
                        $('#toastTitle').html('<i class="fas fa-exclamation-circle text-danger"></i> Error');
                        $('#toastMessage').text(response.message);
                        $('#ajaxToast').toast('show');
                    }
                },
                error: function(xhr, status, error) {
                    // Close the modal
                    $('#deleteModal').modal('hide');
                    
                    // Show error message
                    $('#toastTitle').html('<i class="fas fa-exclamation-circle text-danger"></i> Error');
                    $('#toastMessage').text('An error occurred while processing your request.');
                    $('#ajaxToast').toast('show');
                    
                    console.error(xhr.responseText);
                }
            });
        });
        
        // Configure toast
        $('#ajaxToast').toast({
            delay: 5000
        });
    });
</script>
<!-- Include moment.js for date manipulations -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<?= $this->endSection() ?> 