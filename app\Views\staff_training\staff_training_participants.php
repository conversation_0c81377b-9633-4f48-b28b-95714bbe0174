<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="card-title mb-0">Training Participants</h4>
                        <p class="text-muted small mb-0">Training: <?= esc($training['topic']) ?></p>
                    </div>
                    <div class="col-auto">
                        <a href="<?= base_url('staff/extension/trainings/view/' . $training['id']) ?>" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left"></i> Back to Training
                        </a>
                        <a href="<?= base_url('staff/extension/trainings/participants/export/' . $training['id']) ?>" class="btn btn-info me-2">
                            <i class="fas fa-file-export"></i> Export Participants
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <form id="participantsForm" action="<?= base_url('staff/extension/trainings/participants/batch-save/' . $training['id']) ?>" method="post">
                    <?= csrf_field() ?>
                    
                    <div class="mb-3">
                        <button type="button" class="btn btn-success" id="addRowBtn">
                            <i class="fas fa-plus-circle"></i> Add New Row
                        </button>
                    </div>

                    <div class="table-responsive">
                        <table id="participantsTable" class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Name <span class="text-danger">*</span></th>
                                    <th>Type <span class="text-danger">*</span></th>
                                    <th>Farmer ID</th>
                                    <th>Gender <span class="text-danger">*</span></th>
                                    <th>Age <span class="text-danger">*</span></th>
                                    <th>Phone</th>
                                    <th>Email</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($attendees)): ?>
                                    <tr class="participant-row">
                                        <td>
                                            <input type="text" class="form-control" name="participants[0][name]" required>
                                            <input type="hidden" name="participants[0][id]" value="">
                                        </td>
                                        <td>
                                            <select class="form-select participant-type" name="participants[0][type]" required>
                                                <option value="">Select Type</option>
                                                <option value="Farmer">Farmer</option>
                                                <option value="Extension Officer">Extension Officer</option>
                                                <option value="Government Official">Government Official</option>
                                                <option value="NGO Staff">NGO Staff</option>
                                                <option value="Community Leader">Community Leader</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control farmer-id" name="participants[0][farmer_id]" disabled>
                                        </td>
                                        <td>
                                            <select class="form-select" name="participants[0][gender]" required>
                                                <option value="">Select Gender</option>
                                                <option value="Male">Male</option>
                                                <option value="Female">Female</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </td>
                                        <td>
                                            <input type="number" class="form-control" name="participants[0][age]" min="1" max="120" required>
                                        </td>
                                        <td>
                                            <input type="tel" class="form-control" name="participants[0][phone]">
                                        </td>
                                        <td>
                                            <input type="email" class="form-control" name="participants[0][email]">
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-danger btn-sm delete-row">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($attendees as $index => $attendee): ?>
                                        <tr class="participant-row">
                                            <td>
                                                <input type="text" class="form-control" name="participants[<?= $index ?>][name]" value="<?= esc($attendee['name']) ?>" required>
                                                <input type="hidden" name="participants[<?= $index ?>][id]" value="<?= esc($attendee['id']) ?>">
                                            </td>
                                            <td>
                                                <select class="form-select participant-type" name="participants[<?= $index ?>][type]" required>
                                                    <option value="">Select Type</option>
                                                    <option value="Farmer" <?= $attendee['type'] == 'Farmer' ? 'selected' : '' ?>>Farmer</option>
                                                    <option value="Extension Officer" <?= $attendee['type'] == 'Extension Officer' ? 'selected' : '' ?>>Extension Officer</option>
                                                    <option value="Government Official" <?= $attendee['type'] == 'Government Official' ? 'selected' : '' ?>>Government Official</option>
                                                    <option value="NGO Staff" <?= $attendee['type'] == 'NGO Staff' ? 'selected' : '' ?>>NGO Staff</option>
                                                    <option value="Community Leader" <?= $attendee['type'] == 'Community Leader' ? 'selected' : '' ?>>Community Leader</option>
                                                    <option value="Other" <?= $attendee['type'] == 'Other' ? 'selected' : '' ?>>Other</option>
                                                </select>
                                            </td>
                                            <td>
                                                <input type="text" class="form-control farmer-id" name="participants[<?= $index ?>][farmer_id]" 
                                                       value="<?= esc($attendee['farmer_id'] ?? '') ?>" 
                                                       <?= $attendee['type'] != 'Farmer' ? 'disabled' : '' ?>>
                                            </td>
                                            <td>
                                                <select class="form-select" name="participants[<?= $index ?>][gender]" required>
                                                    <option value="">Select Gender</option>
                                                    <option value="Male" <?= $attendee['gender'] == 'Male' ? 'selected' : '' ?>>Male</option>
                                                    <option value="Female" <?= $attendee['gender'] == 'Female' ? 'selected' : '' ?>>Female</option>
                                                    <option value="Other" <?= $attendee['gender'] == 'Other' ? 'selected' : '' ?>>Other</option>
                                                </select>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control" name="participants[<?= $index ?>][age]" 
                                                       value="<?= esc($attendee['age'] ?? '') ?>" min="1" max="120" required>
                                            </td>
                                            <td>
                                                <input type="tel" class="form-control" name="participants[<?= $index ?>][phone]" 
                                                       value="<?= esc($attendee['phone'] ?? '') ?>">
                                            </td>
                                            <td>
                                                <input type="email" class="form-control" name="participants[<?= $index ?>][email]" 
                                                       value="<?= esc($attendee['email'] ?? '') ?>">
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-danger btn-sm delete-row">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save All Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Toast for showing messages -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="messageToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="toastTitle">Notification</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastMessage">
            Message here
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize toast
    var messageToast = new bootstrap.Toast(document.getElementById('messageToast'));

    // Function to show toast message
    function showToast(title, message, isError = false) {
        $('#toastTitle').html('<i class="fas fa-' + (isError ? 'exclamation-circle text-danger' : 'check-circle text-success') + '"></i> ' + title);
        $('#toastMessage').text(message);
        messageToast.show();
    }

    // Function to get new row HTML
    function getNewRowHtml(index) {
        return `
            <tr class="participant-row">
                <td>
                    <input type="text" class="form-control" name="participants[${index}][name]" required>
                    <input type="hidden" name="participants[${index}][id]" value="">
                </td>
                <td>
                    <select class="form-select participant-type" name="participants[${index}][type]" required>
                        <option value="">Select Type</option>
                        <option value="Farmer">Farmer</option>
                        <option value="Extension Officer">Extension Officer</option>
                        <option value="Government Official">Government Official</option>
                        <option value="NGO Staff">NGO Staff</option>
                        <option value="Community Leader">Community Leader</option>
                        <option value="Other">Other</option>
                    </select>
                </td>
                <td>
                    <input type="text" class="form-control farmer-id" name="participants[${index}][farmer_id]" disabled>
                </td>
                <td>
                    <select class="form-select" name="participants[${index}][gender]" required>
                        <option value="">Select Gender</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control" name="participants[${index}][age]" min="1" max="120" required>
                </td>
                <td>
                    <input type="tel" class="form-control" name="participants[${index}][phone]">
                </td>
                <td>
                    <input type="email" class="form-control" name="participants[${index}][email]">
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm delete-row">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }

    // Add new row button click handler
    $('#addRowBtn').on('click', function() {
        var newIndex = $('.participant-row').length;
        $('#participantsTable tbody').append(getNewRowHtml(newIndex));
    });

    // Delete row button click handler
    $(document).on('click', '.delete-row', function() {
        var rowCount = $('.participant-row').length;
        if (rowCount > 1) {
            $(this).closest('tr').remove();
            reindexRows();
        } else {
            showToast('Error', 'Cannot delete the last row', true);
        }
    });

    // Handle participant type change
    $(document).on('change', '.participant-type', function() {
        var farmerIdInput = $(this).closest('tr').find('.farmer-id');
        if ($(this).val() === 'Farmer') {
            farmerIdInput.prop('disabled', false);
        } else {
            farmerIdInput.prop('disabled', true).val('');
        }
    });

    // Function to reindex rows
    function reindexRows() {
        $('.participant-row').each(function(index) {
            $(this).find('input, select').each(function() {
                var name = $(this).attr('name');
                if (name) {
                    $(this).attr('name', name.replace(/\[\d+\]/, '[' + index + ']'));
                }
            });
        });
    }

    // Form submission handler
    $('#participantsForm').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showToast('Success', response.message);
                    // Optionally reload the page after a delay
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    showToast('Error', response.message, true);
                }
            },
            error: function(xhr, status, error) {
                showToast('Error', 'An error occurred while saving the data', true);
                console.error(xhr.responseText);
            }
        });
    });
});
</script>
<?= $this->endSection() ?> 