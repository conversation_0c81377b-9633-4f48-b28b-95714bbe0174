<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb and <PERSON> Button -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>" class="text-success">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('staff/extension/field-visits') ?>" class="text-success">Field Visits</a></li>
            <li class="breadcrumb-item active" aria-current="page">View Field Visit</li>
        </ol>
    </nav>
    <div>
        <a href="<?= base_url('staff/extension/field-visits/' . $visit['id'] . '/edit') ?>" class="btn btn-primary me-2">
            <i class="fas fa-edit me-1"></i>Edit
        </a>
        <a href="<?= base_url('staff/extension/field-visits') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Back to List
        </a>
    </div>
</div>

<!-- Field Visit Details Card -->
<div class="card mb-4">
    <div class="card-header bg-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Field Visit Details</h5>
        <span class="badge bg-<?= $visit['status'] == 1 ? 'success' : 'secondary' ?>">
            <?= $visit['status'] == 1 ? 'Active' : 'Inactive' ?>
        </span>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- Location Information -->
            <div class="col-md-6">
                <h6 class="border-bottom pb-2 mb-3">Location Information</h6>

                <div class="mb-3 row">
                    <label class="col-sm-4 col-form-label fw-bold">Country:</label>
                    <div class="col-sm-8">
                        <p class="form-control-plaintext"><?= esc($visit['country_name'] ?? 'N/A') ?></p>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label class="col-sm-4 col-form-label fw-bold">Province:</label>
                    <div class="col-sm-8">
                        <p class="form-control-plaintext"><?= esc($visit['province_name'] ?? 'N/A') ?></p>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label class="col-sm-4 col-form-label fw-bold">District:</label>
                    <div class="col-sm-8">
                        <p class="form-control-plaintext"><?= esc($visit['district_name'] ?? 'N/A') ?></p>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label class="col-sm-4 col-form-label fw-bold">LLG:</label>
                    <div class="col-sm-8">
                        <p class="form-control-plaintext"><?= esc($visit['llg_name'] ?? 'N/A') ?></p>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label class="col-sm-4 col-form-label fw-bold">Specific Locations:</label>
                    <div class="col-sm-8">
                        <?php
                            $locations = [];
                            if (!empty($visit['locations'])) {
                                $locationsArray = json_decode($visit['locations'], true);
                                if (is_array($locationsArray)) {
                                    $locations = $locationsArray;
                                }
                            }
                        ?>
                        <?php if (empty($locations)): ?>
                            <p class="form-control-plaintext">No specific locations recorded</p>
                        <?php else: ?>
                            <ul class="list-group">
                                <?php foreach ($locations as $location): ?>
                                    <li class="list-group-item"><?= esc($location) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label class="col-sm-4 col-form-label fw-bold">GPS Coordinates:</label>
                    <div class="col-sm-8">
                        <?php
                            $gpsCoordinates = [];
                            if (!empty($visit['gps'])) {
                                $gpsArray = json_decode($visit['gps'], true);
                                if (is_array($gpsArray)) {
                                    $gpsCoordinates = $gpsArray;
                                }
                            }
                        ?>
                        <?php if (empty($gpsCoordinates)): ?>
                            <p class="form-control-plaintext">No GPS coordinates recorded</p>
                        <?php else: ?>
                            <ul class="list-group">
                                <?php foreach ($gpsCoordinates as $coordinate): ?>
                                    <li class="list-group-item">
                                        <?= esc($coordinate) ?>
                                        <?php
                                            // Try to extract lat/long for map link
                                            $parts = explode(',', $coordinate);
                                            if (count($parts) == 2 && is_numeric(trim($parts[0])) && is_numeric(trim($parts[1]))) {
                                                $lat = trim($parts[0]);
                                                $lng = trim($parts[1]);
                                        ?>
                                            <a href="https://maps.google.com/?q=<?= $lat ?>,<?= $lng ?>" target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                                                <i class="fas fa-map-marker-alt me-1"></i>View on Map
                                            </a>
                                        <?php } ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Visit Details -->
            <div class="col-md-6">
                <h6 class="border-bottom pb-2 mb-3">Visit Details</h6>

                <div class="mb-3 row">
                    <label class="col-sm-4 col-form-label fw-bold">Date Range:</label>
                    <div class="col-sm-8">
                        <p class="form-control-plaintext">
                            <?= date('d M Y', strtotime($visit['date_start'])) ?> -
                            <?= date('d M Y', strtotime($visit['date_end'])) ?>
                            (<?= ceil((strtotime($visit['date_end']) - strtotime($visit['date_start'])) / (60 * 60 * 24)) + 1 ?> days)
                        </p>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label class="col-sm-4 col-form-label fw-bold">Purpose:</label>
                    <div class="col-sm-8">
                        <p class="form-control-plaintext"><?= nl2br(esc($visit['purpose'])) ?></p>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label class="col-sm-4 col-form-label fw-bold">Officers:</label>
                    <div class="col-sm-8">
                        <?php
                            $officers = [];
                            if (!empty($visit['officers'])) {
                                $officersArray = json_decode($visit['officers'], true);
                                if (is_array($officersArray)) {
                                    $officers = $officersArray;
                                }
                            }
                        ?>
                        <?php if (empty($officers)): ?>
                            <p class="form-control-plaintext">No officers assigned</p>
                        <?php else: ?>
                            <ul class="list-group">
                                <?php foreach ($officers as $officer): ?>
                                    <li class="list-group-item"><?= esc($officer['name'] ?? 'Unknown Officer') ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label class="col-sm-4 col-form-label fw-bold">Achievements:</label>
                    <div class="col-sm-8">
                        <?php
                            $achievements = [];
                            if (!empty($visit['achievements'])) {
                                $achievementsArray = json_decode($visit['achievements'], true);
                                if (is_array($achievementsArray)) {
                                    $achievements = $achievementsArray;
                                }
                            }
                        ?>
                        <?php if (empty($achievements)): ?>
                            <p class="form-control-plaintext">No achievements recorded</p>
                        <?php else: ?>
                            <ul class="list-group">
                                <?php foreach ($achievements as $achievement): ?>
                                    <li class="list-group-item"><?= esc($achievement) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-3 row">
                    <label class="col-sm-4 col-form-label fw-bold">Beneficiaries:</label>
                    <div class="col-sm-8">
                        <?php
                            $beneficiaries = [];
                            if (!empty($visit['beneficiaries'])) {
                                $beneficiariesArray = json_decode($visit['beneficiaries'], true);
                                if (is_array($beneficiariesArray)) {
                                    $beneficiaries = $beneficiariesArray;
                                }
                            }
                        ?>
                        <?php if (empty($beneficiaries)): ?>
                            <p class="form-control-plaintext">No beneficiaries recorded</p>
                        <?php else: ?>
                            <ul class="list-group">
                                <?php foreach ($beneficiaries as $beneficiary): ?>
                                    <li class="list-group-item"><?= esc($beneficiary) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-footer bg-white">
        <div class="row text-muted small">
            <div class="col-md-6">
                <p class="mb-0">Created: <?= date('d M Y H:i', strtotime($visit['created_at'])) ?></p>
            </div>
            <div class="col-md-6 text-md-end">
                <?php if (!empty($visit['updated_at']) && $visit['updated_at'] != $visit['created_at']): ?>
                    <p class="mb-0">Last Updated: <?= date('d M Y H:i', strtotime($visit['updated_at'])) ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
